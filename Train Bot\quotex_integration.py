#!/usr/bin/env python3
"""
Enhanced PyQuotex Integration for Trading Bot
Includes WebSocket connection, real-time data fetching, and automatic trade execution
"""

import asyncio
import time
import json
import re
from datetime import datetime, timedelta
from playwright.async_api import async_playwright
import pandas as pd
import numpy as np
import logging

# Import PyQuotex modules
import sys
import os
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from pyquotex.http.login import Login
from pyquotex.http.settings import Settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QuotexBotIntegration:
    """
    Enhanced PyQuotex integration with WebSocket connection and automatic trading
    Handles authentication, real-time data fetching, and automatic trade execution
    """

    def __init__(self, email, password, demo_mode=True):
        self.email = email
        self.password = password
        self.demo_mode = demo_mode
        self.base_url = "https://market-qx.pro"

        # HTTP API components
        self.api_mock = type('obj', (object,), {
            'lang': 'pt',
            'https_url': self.base_url,
            'host': 'market-qx.pro',
            'resource_path': '.',
            'user_data_dir': '.',
            'username': email,
            'password': password,
            'session_data': {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'cookies': None,
                'token': None
            }
        })()

        self.login_module = Login(self.api_mock)
        self.settings_module = Settings(self.api_mock)

        # Connection state
        self.is_authenticated = False
        self.balance_data = {}
        self.candle_cache = {}
        self.cache_timeout = 30  # seconds

        # Enhanced browser automation for WebSocket and trading
        self.browser = None
        self.context = None
        self.page = None
        self.websocket_connected = False
        self.websocket_data = []
        self.tick_data = {}
        self.current_prices = {}

        # Trading state
        self.is_logged_in_browser = False
        self.last_balance_check = 0
        self.current_balance = 0
        
    async def connect(self):
        """Enhanced connection with WebSocket and browser automation"""
        try:
            print("🔐 Connecting to Quotex...")

            # First, try HTTP authentication
            status, message = await self.login_module(self.email, self.password)

            if status:
                print("✅ HTTP authentication successful!")
                self.is_authenticated = True

                # Get cookies for future requests
                if hasattr(self.login_module, 'response') and self.login_module.response:
                    cookies = self.login_module.response.cookies
                    cookie_string = "; ".join([f"{cookie.name}={cookie.value}" for cookie in cookies])
                    self.api_mock.session_data["cookies"] = cookie_string

            # Now establish browser connection with WebSocket
            print("🌐 Establishing browser connection with WebSocket...")
            browser_connected = await self._connect_browser_websocket()

            if browser_connected:
                print("✅ WebSocket connection established!")
                return True
            else:
                print("⚠️ WebSocket connection failed, but HTTP auth succeeded")
                return True  # Still return True as HTTP auth worked

        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False

    async def _connect_browser_websocket(self):
        """Establish browser connection with WebSocket for real-time data"""
        try:
            # Launch browser
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(
                headless=False,  # Keep visible for debugging
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )

            self.context = await self.browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            )

            self.page = await self.context.new_page()

            # Set up WebSocket monitoring
            self._setup_websocket_handlers()

            # Navigate to Quotex
            trade_url = f"{self.base_url}/pt/trade" if self.demo_mode else f"{self.base_url}/pt/trade"
            await self.page.goto(trade_url, timeout=30000)
            await self.page.wait_for_load_state("networkidle", timeout=20000)

            # Login via browser
            login_success = await self._browser_login()

            if login_success:
                # Wait for WebSocket connection
                await asyncio.sleep(5)
                print("🔌 WebSocket connection established")
                self.websocket_connected = True
                return True
            else:
                print("❌ Browser login failed")
                return False

        except Exception as e:
            print(f"❌ Browser WebSocket connection error: {e}")
            return False

    def _setup_websocket_handlers(self):
        """Set up WebSocket message handlers for real-time data"""
        def handle_websocket(ws):
            def on_framereceived(payload):
                try:
                    if isinstance(payload, bytes):
                        message = payload.decode('utf-8', errors='ignore')
                    else:
                        message = str(payload)

                    # Process real-time tick data
                    self._process_websocket_message(message)

                except Exception as e:
                    logger.debug(f"WebSocket message processing error: {e}")

            ws.on("framereceived", on_framereceived)

        self.page.on("websocket", handle_websocket)

    def _process_websocket_message(self, message):
        """Process WebSocket messages for tick data"""
        try:
            # Look for tick data patterns: ["ASSET_otc", timestamp, price, direction]
            tick_pattern = r'\["([^"]+_otc)",([0-9.]+),([0-9.]+),([01])\]'
            matches = re.findall(tick_pattern, message)

            for match in matches:
                asset, timestamp, price, direction = match
                timestamp = float(timestamp)
                price = float(price)
                direction = int(direction)

                # Store tick data
                if asset not in self.tick_data:
                    self.tick_data[asset] = []

                self.tick_data[asset].append({
                    'timestamp': timestamp,
                    'price': price,
                    'direction': direction,
                    'datetime': datetime.fromtimestamp(timestamp)
                })

                # Update current price
                self.current_prices[asset] = price

                # Keep only recent data (last 1000 ticks)
                if len(self.tick_data[asset]) > 1000:
                    self.tick_data[asset] = self.tick_data[asset][-1000:]

        except Exception as e:
            logger.debug(f"Tick data processing error: {e}")

    async def _browser_login(self):
        """Login via browser automation"""
        try:
            # Check if already logged in
            current_url = self.page.url
            if "trade" in current_url and "login" not in current_url:
                print("✅ Already logged in to browser")
                self.is_logged_in_browser = True
                if self.demo_mode:
                    await self._switch_to_demo_mode()
                return True

            # Wait for page to load completely
            await asyncio.sleep(3)

            # Look for email input with more selectors
            email_selectors = [
                'input[name="email"]',
                'input[type="email"]',
                'input[placeholder*="email" i]',
                'input[placeholder*="e-mail" i]',
                'input[id*="email" i]',
                'input[class*="email" i]',
                'input[data-testid*="email" i]',
                'form input[type="text"]',
                'form input:first-of-type'
            ]

            email_input = None
            for selector in email_selectors:
                try:
                    email_input = await self.page.wait_for_selector(selector, timeout=3000)
                    if email_input and await email_input.is_visible():
                        break
                except:
                    continue

            if not email_input:
                print("⚠️ Email input not found, checking if login is needed...")
                # Maybe already logged in or different page structure
                await asyncio.sleep(5)
                current_url = self.page.url
                if "trade" in current_url:
                    print("✅ Appears to be logged in already")
                    self.is_logged_in_browser = True
                    if self.demo_mode:
                        await self._switch_to_demo_mode()
                    return True
                else:
                    print("❌ Email input not found and not on trade page")
                    return False

            # Fill email
            await email_input.fill(self.email)
            await asyncio.sleep(1)

            # Look for password input with more selectors
            password_selectors = [
                'input[name="password"]',
                'input[type="password"]',
                'input[placeholder*="password" i]',
                'input[placeholder*="senha" i]',
                'input[id*="password" i]',
                'input[class*="password" i]',
                'input[data-testid*="password" i]',
                'form input[type="password"]',
                'form input:last-of-type'
            ]

            password_input = None
            for selector in password_selectors:
                try:
                    password_input = await self.page.wait_for_selector(selector, timeout=3000)
                    if password_input and await password_input.is_visible():
                        break
                except:
                    continue

            if not password_input:
                print("❌ Password input not found")
                return False

            # Fill password
            await password_input.fill(self.password)
            await asyncio.sleep(1)

            # Submit login
            await password_input.press('Enter')

            # Wait for login to complete
            await asyncio.sleep(5)
            await self.page.wait_for_load_state("networkidle", timeout=20000)

            # Check if login was successful
            current_url = self.page.url
            if "trade" in current_url and "login" not in current_url:
                print("✅ Browser login successful")
                self.is_logged_in_browser = True

                # Switch to demo mode if needed
                if self.demo_mode:
                    await self._switch_to_demo_mode()

                return True
            else:
                print("❌ Browser login failed - still on login page")
                return False

        except Exception as e:
            print(f"❌ Browser login error: {e}")
            return False

    async def _switch_to_demo_mode(self):
        """Switch to demo mode in browser"""
        try:
            # Look for demo mode switch
            demo_selectors = [
                'button[data-mode="demo"]',
                'button[data-account="demo"]',
                'button:has-text("Demo")',
                'button:has-text("DEMO")',
                '.demo-button',
                '[data-testid="demo-button"]'
            ]

            for selector in demo_selectors:
                try:
                    demo_button = await self.page.wait_for_selector(selector, timeout=3000)
                    if demo_button and await demo_button.is_visible():
                        await demo_button.click()
                        await asyncio.sleep(2)
                        print("🔵 Switched to demo mode in browser")
                        return True
                except:
                    continue

            print("⚠️ Demo mode switch not found, assuming already in demo mode")
            return True

        except Exception as e:
            print(f"❌ Demo mode switch error: {e}")
            return False
    
    async def get_balance(self):
        """Get current account balance"""
        try:
            if not self.is_authenticated:
                await self.connect()
            
            settings_data = self.settings_module.get_settings()
            
            if isinstance(settings_data, dict) and 'data' in settings_data:
                account_data = settings_data['data']
                
                self.balance_data = {
                    'live_balance': float(account_data.get('liveBalance', 0)),
                    'demo_balance': float(account_data.get('demoBalance', 0)),
                    'currency': account_data.get('currencyCode', 'USD'),
                    'user_id': account_data.get('nickname', 'Unknown'),
                    'email': account_data.get('email', 'Unknown'),
                    'timestamp': time.time()
                }
                
                current_balance = self.balance_data['demo_balance'] if self.demo_mode else self.balance_data['live_balance']
                return current_balance
            
            return 0.0
            
        except Exception as e:
            print(f"❌ Error getting balance: {e}")
            return 0.0
    
    def change_account(self, account_type):
        """Change account type (PRACTICE/REAL)"""
        if account_type == "PRACTICE":
            self.demo_mode = True
            print("🔵 Switched to Demo account")
        elif account_type == "REAL":
            self.demo_mode = False
            print("🔴 Switched to Live account")
    
    async def get_candles_browser(self, asset, period=60, count=100):
        """Get candle data using real-time WebSocket data"""
        try:
            # Check cache first
            cache_key = f"{asset}_{period}_{count}"
            if cache_key in self.candle_cache:
                cache_time, cached_data = self.candle_cache[cache_key]
                if time.time() - cache_time < self.cache_timeout:
                    return cached_data

            print(f"📊 Fetching candle data for {asset} from WebSocket...")

            # Use real-time tick data to build candles
            candle_data = self._build_candles_from_ticks(asset, period, count)

            if candle_data and len(candle_data) > 0:
                # Cache the data
                self.candle_cache[cache_key] = (time.time(), candle_data)
                print(f"✅ Retrieved {len(candle_data)} candles for {asset} from WebSocket")
                return candle_data
            else:
                print(f"⚠️ No WebSocket data available for {asset}, waiting for data...")
                # Wait a bit for data to accumulate
                await asyncio.sleep(5)

                # Try again
                candle_data = self._build_candles_from_ticks(asset, period, count)
                if candle_data and len(candle_data) > 0:
                    self.candle_cache[cache_key] = (time.time(), candle_data)
                    print(f"✅ Retrieved {len(candle_data)} candles for {asset} after waiting")
                    return candle_data
                else:
                    print(f"❌ No candle data available for {asset}")
                    return None

        except Exception as e:
            print(f"❌ Error getting candles for {asset}: {e}")
            return None

    def _build_candles_from_ticks(self, asset, period=60, count=100):
        """Build OHLC candles from tick data"""
        try:
            if asset not in self.tick_data or len(self.tick_data[asset]) == 0:
                return None

            ticks = self.tick_data[asset]
            if len(ticks) < 10:  # Need minimum ticks
                return None

            # Sort ticks by timestamp
            ticks = sorted(ticks, key=lambda x: x['timestamp'])

            # Build candles
            candles = []
            current_candle = None

            for tick in ticks:
                timestamp = tick['timestamp']
                price = tick['price']

                # Calculate candle start time
                candle_start = int(timestamp // period) * period

                if current_candle is None or current_candle['time'] != candle_start:
                    # Save previous candle
                    if current_candle is not None:
                        candles.append(current_candle)

                    # Start new candle
                    current_candle = {
                        'time': candle_start,
                        'open': price,
                        'high': price,
                        'low': price,
                        'close': price,
                        'volume': 1
                    }
                else:
                    # Update existing candle
                    current_candle['high'] = max(current_candle['high'], price)
                    current_candle['low'] = min(current_candle['low'], price)
                    current_candle['close'] = price
                    current_candle['volume'] += 1

            # Add final candle
            if current_candle is not None:
                candles.append(current_candle)

            # Return last 'count' candles
            return candles[-count:] if len(candles) > count else candles

        except Exception as e:
            print(f"❌ Error building candles from ticks: {e}")
            return None
    
    async def _fetch_candles_with_browser(self, asset, period=60, count=100):
        """Fetch candle data using browser automation"""
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                )
                page = await context.new_page()
                
                # Capture WebSocket data
                candle_data = []
                tick_data = []
                
                def handle_websocket(ws):
                    def on_framereceived(payload):
                        try:
                            if isinstance(payload, bytes):
                                message = payload.decode('utf-8', errors='ignore')
                            else:
                                message = str(payload)
                            
                            # Look for tick data: ["ASSET_otc", timestamp, price, direction]
                            if asset in message and re.search(r'\d+\.\d{4,}', message):
                                match = re.search(rf'\["{asset}",([0-9.]+),([0-9.]+),([01])\]', message)
                                if match:
                                    timestamp = float(match.group(1))
                                    price = float(match.group(2))
                                    direction = int(match.group(3))
                                    
                                    tick_data.append({
                                        'timestamp': timestamp,
                                        'price': price,
                                        'direction': direction
                                    })
                        except:
                            pass
                    
                    ws.on("framereceived", on_framereceived)
                
                page.on("websocket", handle_websocket)
                
                # Navigate and login
                await page.goto(f"{self.base_url}/pt/trade", timeout=30000)
                await page.wait_for_load_state("networkidle", timeout=20000)
                
                # Quick login if needed
                email_input = await page.query_selector('input[name="email"], input[type="email"]')
                if email_input and await email_input.is_visible():
                    await email_input.fill(self.email)
                    password_input = await page.query_selector('input[name="password"], input[type="password"]')
                    if password_input:
                        await password_input.fill(self.password)
                        await password_input.press('Enter')
                        await page.wait_for_load_state("networkidle", timeout=20000)
                
                # Wait for WebSocket connection and collect data
                await asyncio.sleep(10)
                
                # Try to select the asset
                asset_selectors = [f'text={asset.replace("_otc", "")}', f'[data-asset="{asset}"]']
                for selector in asset_selectors:
                    try:
                        element = await page.query_selector(selector)
                        if element:
                            await element.click()
                            await asyncio.sleep(2)
                            break
                    except:
                        continue
                
                # Collect more data
                await asyncio.sleep(15)
                
                await browser.close()
                
                # Convert tick data to candles
                if tick_data:
                    candle_data = self._convert_ticks_to_candles(tick_data, period)
                    return candle_data[-count:] if len(candle_data) > count else candle_data
                
                return None
                
        except Exception as e:
            print(f"❌ Browser candle fetch error: {e}")
            return None
    
    def _convert_ticks_to_candles(self, tick_data, period=60):
        """Convert tick data to OHLC candles"""
        if not tick_data:
            return []
        
        # Sort by timestamp
        tick_data.sort(key=lambda x: x['timestamp'])
        
        candles = []
        current_candle = None
        
        for tick in tick_data:
            timestamp = tick['timestamp']
            price = tick['price']
            
            # Calculate candle start time
            candle_start = int(timestamp // period) * period
            
            if current_candle is None or current_candle['time'] != candle_start:
                # Save previous candle
                if current_candle is not None:
                    candles.append(current_candle)
                
                # Start new candle
                current_candle = {
                    'time': candle_start,
                    'open': price,
                    'high': price,
                    'low': price,
                    'close': price,
                    'volume': 1
                }
            else:
                # Update existing candle
                current_candle['high'] = max(current_candle['high'], price)
                current_candle['low'] = min(current_candle['low'], price)
                current_candle['close'] = price
                current_candle['volume'] += 1
        
        # Add final candle
        if current_candle is not None:
            candles.append(current_candle)
        
        return candles
    
    async def trade(self, action, amount, asset, duration):
        """Execute trade automatically via browser automation"""
        try:
            print(f"🎯 Trade Request: {action.upper()} {asset} ${amount} {duration}s")

            # Check if browser is connected, if not try to establish connection
            if not self.is_logged_in_browser or not self.page:
                print("⚠️ Browser not fully connected, attempting to establish connection...")
                if self.page:
                    # Try to navigate to trade page and login
                    try:
                        trade_url = f"{self.base_url}/pt/trade"
                        await self.page.goto(trade_url, timeout=30000)
                        await asyncio.sleep(3)

                        # Check if we're on trade page now
                        current_url = self.page.url
                        if "trade" in current_url:
                            print("✅ Successfully navigated to trade page")
                            self.is_logged_in_browser = True
                            if self.demo_mode:
                                await self._switch_to_demo_mode()
                        else:
                            print("❌ Failed to reach trade page")
                            return False, "Cannot reach trade page"
                    except Exception as e:
                        print(f"❌ Failed to establish browser connection: {e}")
                        return False, "Browser connection failed"
                else:
                    print("❌ No browser page available")
                    return False, "No browser page"

            # Check balance
            balance_before = await self.get_balance()
            if balance_before < amount:
                return False, f"Insufficient balance: ${balance_before:.2f} < ${amount}"

            print(f"💰 Balance before trade: ${balance_before:.2f}")

            # Execute trade via browser automation
            trade_success = await self._execute_browser_trade(action, amount, asset, duration)

            if trade_success:
                # Wait a moment for trade to process
                await asyncio.sleep(3)

                # Check balance after trade
                balance_after = await self.get_balance()
                print(f"💰 Balance after trade: ${balance_after:.2f}")

                # Verify trade was executed (balance should be reduced by trade amount)
                if balance_after < balance_before:
                    balance_diff = balance_before - balance_after
                    print(f"✅ Trade executed successfully! Balance reduced by ${balance_diff:.2f}")
                    return True, {
                        'status': 'executed',
                        'balance_before': balance_before,
                        'balance_after': balance_after,
                        'amount_deducted': balance_diff,
                        'asset': asset,
                        'direction': action,
                        'amount': amount,
                        'duration': duration
                    }
                else:
                    print("⚠️ Trade may not have been executed (balance unchanged)")
                    return False, "Trade execution uncertain - balance unchanged"
            else:
                print("❌ Trade execution failed")
                return False, "Browser trade execution failed"

        except Exception as e:
            print(f"❌ Trade execution error: {e}")
            return False, str(e)

    async def _execute_browser_trade(self, action, amount, asset, duration):
        """Execute trade via browser automation"""
        try:
            print(f"🚀 Executing {action.upper()} trade for {asset}...")

            # Step 1: Select asset
            asset_selected = await self._select_asset(asset)
            if not asset_selected:
                print("❌ Failed to select asset")
                return False

            # Step 2: Set trade amount
            amount_set = await self._set_trade_amount(amount)
            if not amount_set:
                print("❌ Failed to set trade amount")
                return False

            # Step 3: Set duration (if available)
            await self._set_duration(duration)

            # Step 4: Execute trade (CALL or PUT)
            trade_executed = await self._click_trade_button(action)
            if not trade_executed:
                print("❌ Failed to execute trade")
                return False

            print(f"✅ Trade execution completed: {action.upper()} {asset} ${amount}")
            return True

        except Exception as e:
            print(f"❌ Browser trade execution error: {e}")
            return False

    async def _select_asset(self, asset):
        """Select trading asset in browser"""
        try:
            # Remove _otc suffix for display
            display_asset = asset.replace("_otc", "")

            # Look for asset selector
            asset_selectors = [
                f'[data-asset="{asset}"]',
                f'[data-asset="{display_asset}"]',
                f'button:has-text("{display_asset}")',
                f'div:has-text("{display_asset}")',
                '.asset-item',
                '.currency-item'
            ]

            for selector in asset_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=3000)
                    if element and await element.is_visible():
                        await element.click()
                        await asyncio.sleep(1)
                        print(f"✅ Selected asset: {asset}")
                        return True
                except:
                    continue

            print(f"⚠️ Asset selector not found for {asset}, assuming already selected")
            return True

        except Exception as e:
            print(f"❌ Asset selection error: {e}")
            return False

    async def _set_trade_amount(self, amount):
        """Set trade amount in browser"""
        try:
            # Look for amount input
            amount_selectors = [
                'input[data-testid="amount-input"]',
                'input[name="amount"]',
                'input[placeholder*="amount" i]',
                'input[type="number"]',
                '.amount-input input',
                '.trade-amount input'
            ]

            for selector in amount_selectors:
                try:
                    amount_input = await self.page.wait_for_selector(selector, timeout=3000)
                    if amount_input and await amount_input.is_visible():
                        # Clear and set amount
                        await amount_input.click()
                        await amount_input.fill("")
                        await amount_input.type(str(amount))
                        await asyncio.sleep(1)
                        print(f"✅ Set trade amount: ${amount}")
                        return True
                except:
                    continue

            print(f"⚠️ Amount input not found, assuming default amount")
            return True

        except Exception as e:
            print(f"❌ Amount setting error: {e}")
            return False

    async def _set_duration(self, duration):
        """Set trade duration in browser"""
        try:
            # Look for duration selector
            duration_selectors = [
                f'button[data-duration="{duration}"]',
                f'button:has-text("{duration}s")',
                f'button:has-text("{duration//60}m")',
                '.duration-button',
                '.time-button'
            ]

            for selector in duration_selectors:
                try:
                    duration_button = await self.page.wait_for_selector(selector, timeout=3000)
                    if duration_button and await duration_button.is_visible():
                        await duration_button.click()
                        await asyncio.sleep(1)
                        print(f"✅ Set duration: {duration}s")
                        return True
                except:
                    continue

            print(f"⚠️ Duration selector not found, using default duration")
            return True

        except Exception as e:
            print(f"❌ Duration setting error: {e}")
            return False

    async def _click_trade_button(self, action):
        """Click the trade button (CALL or PUT)"""
        try:
            if action.lower() == 'call':
                # Look for CALL/HIGHER/UP button
                call_selectors = [
                    'button[data-direction="call"]',
                    'button[data-direction="up"]',
                    'button:has-text("CALL")',
                    'button:has-text("HIGHER")',
                    'button:has-text("UP")',
                    '.call-button',
                    '.higher-button',
                    '.up-button'
                ]

                for selector in call_selectors:
                    try:
                        call_button = await self.page.wait_for_selector(selector, timeout=3000)
                        if call_button and await call_button.is_visible():
                            await call_button.click()
                            await asyncio.sleep(2)
                            print(f"✅ Clicked CALL button")
                            return True
                    except:
                        continue

            else:  # PUT
                # Look for PUT/LOWER/DOWN button
                put_selectors = [
                    'button[data-direction="put"]',
                    'button[data-direction="down"]',
                    'button:has-text("PUT")',
                    'button:has-text("LOWER")',
                    'button:has-text("DOWN")',
                    '.put-button',
                    '.lower-button',
                    '.down-button'
                ]

                for selector in put_selectors:
                    try:
                        put_button = await self.page.wait_for_selector(selector, timeout=3000)
                        if put_button and await put_button.is_visible():
                            await put_button.click()
                            await asyncio.sleep(2)
                            print(f"✅ Clicked PUT button")
                            return True
                    except:
                        continue

            print(f"❌ {action.upper()} button not found")
            return False

        except Exception as e:
            print(f"❌ Trade button click error: {e}")
            return False
    
    def check_asset_open(self, asset):
        """Check if asset is available for trading"""
        # For OTC pairs, they're available 24/7
        if "_otc" in asset:
            return (asset, True, True)  # (asset_name, exists, is_open)
        else:
            # For live pairs, assume they're available during market hours
            return (asset, True, True)
    
    @property
    def check_connect(self):
        """Check if connected"""
        return self.is_authenticated
    
    async def close(self):
        """Close connection and cleanup browser"""
        try:
            self.is_authenticated = False
            self.websocket_connected = False

            if self.page:
                await self.page.close()
                self.page = None

            if self.context:
                await self.context.close()
                self.context = None

            if self.browser:
                await self.browser.close()
                self.browser = None

            print("🔌 Quotex connection and browser closed")

        except Exception as e:
            print(f"❌ Error closing connection: {e}")

# Global instance
quotex_integration = None

def get_quotex_client(email, password, demo_mode=True):
    """Get or create Quotex client instance"""
    global quotex_integration
    
    if quotex_integration is None:
        quotex_integration = QuotexBotIntegration(email, password, demo_mode)
    
    return quotex_integration
