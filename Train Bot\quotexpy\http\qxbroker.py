import re
import json
import time
import pickle
import requests
from pathlib import Path
from bs4 import <PERSON><PERSON><PERSON><PERSON>
from typing import <PERSON><PERSON>, Any
import undetected_chromedriver as uc
from quotexpy.exceptions import QuotexAuthError


class Browser(object):
    email = None
    password = None
    on_ping_code = None
    headless = None

    base_url = "market-qx.pro"
    https_base_url = f"https://{base_url}"

    def __init__(self, api):
        self.api = api

    def get_cookies_and_ssid(self) -> Tuple[Any, str]:
        try:
            self.browser = uc.Chrome(headless=self.headless, use_subprocess=False)
        except TypeError as exc:
            raise SystemError("Chrome is not installed, did you forget?") from exc
        # Use the working sign-in URL
        login_url = f"{self.https_base_url}/en/sign-in"

        # Add headers to avoid 403 errors
        self.browser.execute_cdp_cmd('Network.setUserAgentOverride', {
            "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        })

        print(f"Navigating to: {login_url}")
        self.browser.get(login_url)

        # Wait for page to load completely
        time.sleep(8)

        if self.browser.current_url != f"{self.https_base_url}/en/trade":
            print(f"Attempting login at: {login_url}")

            # Wait for login form to be ready with more comprehensive checks
            max_wait = 15
            form_found = False

            for i in range(max_wait):
                try:
                    # Check multiple possible selectors for login form elements
                    selectors_to_check = [
                        "input[type='email']",
                        "input[name='email']",
                        "#email",
                        "input[placeholder*='email' i]",
                        "input[placeholder*='Email' i]",
                        ".email-input",
                        "input.email",
                        "form input[type='text']",
                        "form input:first-of-type"
                    ]

                    for selector in selectors_to_check:
                        try:
                            email_field = self.browser.find_element(uc.By.CSS_SELECTOR, selector)
                            if email_field.is_displayed() and email_field.is_enabled():
                                form_found = True
                                break
                        except:
                            continue

                    if form_found:
                        break

                    # Also check if page has loaded by looking for common elements
                    page_indicators = ["form", ".login", ".sign-in", "#login", "button[type='submit']"]
                    for indicator in page_indicators:
                        try:
                            element = self.browser.find_element(uc.By.CSS_SELECTOR, indicator)
                            if element.is_displayed():
                                print(f"Found page indicator: {indicator}")
                                break
                        except:
                            continue

                except Exception as e:
                    print(f"Waiting for form... attempt {i+1}/{max_wait}: {str(e)}")

                time.sleep(2)  # Increased wait time

            if not form_found:
                # Try to get page source for debugging
                try:
                    page_source = self.browser.page_source
                    print(f"Page title: {self.browser.title}")
                    print(f"Current URL: {self.browser.current_url}")
                    print(f"Page source length: {len(page_source)}")

                    # Check if we're on the right page
                    if "sign-in" in self.browser.current_url.lower() or "login" in page_source.lower():
                        print("On login page but form elements not found")
                    else:
                        print("Not on expected login page")

                except Exception as e:
                    print(f"Could not get page info: {str(e)}")

                raise QuotexAuthError("Login form not found after waiting - page may have changed structure")

            # Find and fill email field with more comprehensive search
            email_selectors = [
                "input[type='email']",
                "input[name='email']",
                "#email",
                "input[placeholder*='email' i]",
                "input[placeholder*='Email' i]",
                ".email-input",
                "input.email",
                "form input[type='text']:first-of-type",
                "form input:first-of-type",
                "input[autocomplete='email']",
                "input[autocomplete='username']"
            ]

            email_field = None
            for selector in email_selectors:
                try:
                    email_field = self.browser.find_element(uc.By.CSS_SELECTOR, selector)
                    if email_field.is_displayed() and email_field.is_enabled():
                        print(f"Found email field with selector: {selector}")
                        break
                except:
                    continue

            if not email_field:
                # Try XPath selectors as fallback
                xpath_selectors = [
                    "//input[@type='email']",
                    "//input[@name='email']",
                    "//input[contains(@placeholder, 'email')]",
                    "//input[contains(@placeholder, 'Email')]",
                    "//form//input[1]"
                ]

                for xpath in xpath_selectors:
                    try:
                        email_field = self.browser.find_element(uc.By.XPATH, xpath)
                        if email_field.is_displayed() and email_field.is_enabled():
                            print(f"Found email field with XPath: {xpath}")
                            break
                    except:
                        continue

            if not email_field:
                # Last resort: try JavaScript to find any input field
                try:
                    email_field = self.browser.execute_script("""
                        var inputs = document.querySelectorAll('input[type="text"], input[type="email"], input:not([type])');
                        for (var i = 0; i < inputs.length; i++) {
                            if (inputs[i].offsetParent !== null) {
                                return inputs[i];
                            }
                        }
                        return null;
                    """)
                    if email_field:
                        print("Found email field using JavaScript")
                except:
                    pass

            if not email_field:
                raise QuotexAuthError("Could not find email input field after comprehensive search")

            # Clear and enter email
            email_field.clear()
            email_field.send_keys(self.email)
            time.sleep(1)

            # Find and fill password field
            password_selectors = [
                "input[type='password']",
                "input[name='password']",
                "#password",
                "input[placeholder*='password' i]",
                "input[placeholder*='Password' i]",
                ".password-input",
                "input.password"
            ]

            password_field = None
            for selector in password_selectors:
                try:
                    password_field = self.browser.find_element(uc.By.CSS_SELECTOR, selector)
                    if password_field.is_displayed():
                        break
                except:
                    continue

            if not password_field:
                raise QuotexAuthError("Could not find password input field")

            # Clear and enter password
            password_field.clear()
            password_field.send_keys(self.password)
            time.sleep(1)

            # Find and click submit button
            submit_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button.login-btn",
                "button.submit-btn",
                ".login-button",
                ".submit-button",
                "button:contains('Sign in')",
                "button:contains('Login')",
                "button:contains('Enter')"
            ]

            submit_button = None
            for selector in submit_selectors:
                try:
                    submit_button = self.browser.find_element(uc.By.CSS_SELECTOR, selector)
                    if submit_button.is_displayed() and submit_button.is_enabled():
                        break
                except:
                    continue

            if not submit_button:
                # Try XPath selectors
                xpath_selectors = [
                    "//button[@type='submit']",
                    "//input[@type='submit']",
                    "//button[contains(text(), 'Sign in')]",
                    "//button[contains(text(), 'Login')]",
                    "//button[contains(text(), 'Enter')]",
                    "//button[contains(@class, 'login')]",
                    "//button[contains(@class, 'submit')]"
                ]

                for xpath in xpath_selectors:
                    try:
                        submit_button = self.browser.find_element(uc.By.XPATH, xpath)
                        if submit_button.is_displayed() and submit_button.is_enabled():
                            break
                    except:
                        continue

            if not submit_button:
                raise QuotexAuthError("Could not find submit button")

            # Click submit button
            try:
                submit_button.click()
            except:
                # Try JavaScript click as fallback
                self.browser.execute_script("arguments[0].click();", submit_button)

            print("Login form submitted, waiting for redirect...")
            time.sleep(3)

            time.sleep(5)

        try:
            code_input = self.browser.find_element(uc.By.NAME, "code")
            if code_input.is_displayed():
                code = self.on_ping_code()
                code_input.send_keys(code)
                btn = self.browser.find_element(uc.By.XPATH, "//button[@type='submit']")
                btn.click()
        except:
            pass

        cookies = self.browser.get_cookies()
        self.api.cookies = cookies
        soup = BeautifulSoup(self.browser.page_source, "html.parser")
        user_agent = self.browser.execute_script("return navigator.userAgent;")
        self.api.user_agent = user_agent
        try:
            script: str = soup.find_all("script", {"type": "text/javascript"})[1].get_text()
        except Exception as exc:
            raise QuotexAuthError("incorrect username or password") from exc
        finally:
            self.close()
        match = re.sub("window.settings = ", "", script.strip().replace(";", ""))

        dx: dict = json.loads(match)
        ssid = dx.get("token")

        cookiejar = requests.utils.cookiejar_from_dict({c["name"]: c["value"] for c in cookies})
        cookie_string = "; ".join([f"{c.name}={c.value}" for c in cookiejar])
        output_file = Path(".session.pkl")
        output_file.parent.mkdir(exist_ok=True, parents=True)

        data = {}
        if output_file.is_file():
            with output_file.open("rb") as file:
                data = pickle.load(file)

        data[self.email] = [{"cookies": cookie_string, "ssid": ssid, "user_agent": user_agent}]
        with output_file.open("wb") as file:
            pickle.dump(data, file)

        return ssid, cookie_string

    def close(self):
        try:
            time.sleep(0.2)
            self.browser.close()
        except:
            pass
