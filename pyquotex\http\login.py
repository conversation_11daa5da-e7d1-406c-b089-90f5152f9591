import re
import json
import sys
import asyncio
from pathlib import Path
from pyquotex.http.navigator import Browser


class Login(Browser):
    """Class for Quotex login resource."""

    url = ""
    cookies = None
    ssid = None
    base_url = 'market-qx.pro'
    https_base_url = f'https://{base_url}'

    def __init__(self, api, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.api = api
        self.html = None
        self.headers = self.get_headers()

        # ✅ ADDING ENHANCED HEADERS TO BYPASS CLOUDFLARE
        self.headers["User-Agent"] = (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        self.headers["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"
        self.headers["Accept-Language"] = "en-US,en;q=0.9,pt;q=0.8"
        self.headers["Accept-Encoding"] = "gzip, deflate, br"
        self.headers["Cache-Control"] = "no-cache"
        self.headers["Pragma"] = "no-cache"
        self.headers["Sec-Ch-Ua"] = '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"'
        self.headers["Sec-Ch-Ua-Mobile"] = "?0"
        self.headers["Sec-Ch-Ua-Platform"] = '"Windows"'
        self.headers["Sec-Fetch-Dest"] = "document"
        self.headers["Sec-Fetch-Mode"] = "navigate"
        self.headers["Sec-Fetch-Site"] = "none"
        self.headers["Sec-Fetch-User"] = "?1"
        self.headers["Upgrade-Insecure-Requests"] = "1"

        self.full_url = f"{self.https_base_url}/{api.lang}"

    def get_token(self):
        import time
        import random

        # Add random delay to avoid being detected as bot
        time.sleep(random.uniform(1, 3))

        self.headers["Connection"] = "keep-alive"
        self.headers["Accept-Encoding"] = "gzip, deflate, br"
        self.headers["Accept-Language"] = "pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3"
        self.headers["Accept"] = (
            "text/html,application/xhtml+xml,application/xml;q=0.9,"
            "image/avif,image/webp,*/*;q=0.8"
        )
        self.headers["Referer"] = f"{self.full_url}/sign-in"
        self.headers["Upgrade-Insecure-Requests"] = "1"
        self.headers["Sec-Ch-Ua-Mobile"] = "?0"
        self.headers["Sec-Ch-Ua-Platform"] = '"Windows"'
        self.headers["Sec-Fetch-Site"] = "same-origin"
        self.headers["Sec-Fetch-User"] = "?1"
        self.headers["Sec-Fetch-Dest"] = "document"
        self.headers["Sec-Fetch-Mode"] = "navigate"
        self.headers["Dnt"] = "1"

        # Try multiple times with different delays
        for attempt in range(3):
            try:
                response = self.send_request(
                    "GET",
                    f"{self.full_url}/sign-in/modal/"
                )

                # Check if we got a valid response
                if response.status_code == 200:
                    html = self.get_soup()
                    match = html.find("input", {"name": "_token"})
                    if match:
                        token = match.get("value")
                        if token:
                            return token

                # If we get here, either status wasn't 200 or no token found
                print(f"Token attempt {attempt + 1}: Status {response.status_code}")
                if "cloudflare" in response.text.lower() or "just a moment" in response.text.lower():
                    print("Cloudflare challenge detected, waiting...")
                    time.sleep(random.uniform(5, 10))
                else:
                    time.sleep(random.uniform(2, 5))

            except Exception as e:
                print(f"Token attempt {attempt + 1} failed: {e}")
                time.sleep(random.uniform(3, 6))

        print("Failed to get token after 3 attempts")
        return None

    async def awaiting_pin(self, data, input_message):
        self.headers["Content-Type"] = "application/x-www-form-urlencoded"
        self.headers["Referer"] = f"{self.full_url}/sign-in/modal"
        data["keep_code"] = 1
        try:
            code = input(input_message)
            if not code.isdigit():
                print("Please enter a valid code.")
                await self.awaiting_pin(data, input_message)
            data["code"] = code
        except KeyboardInterrupt:
            print("\nClosing program.")
            sys.exit()

        await asyncio.sleep(1)
        self.send_request(
            method="POST",
            url=f"{self.full_url}/sign-in/modal",
            data=data
        )

    def get_profile(self):
        self.response = self.send_request(
            method="GET",
            url=f"{self.full_url}/trade"
        )
        if self.response:
            script = self.get_soup().find_all(
                "script",
                {"type": "text/javascript"}
            )
            script = script[0].get_text() if script else "{}"
            match = re.sub(
                "window.settings = ",
                "",
                script.strip().replace(";", "")
            )
            self.cookies = self.get_cookies()
            self.ssid = json.loads(match).get("token")
            self.api.session_data["cookies"] = self.cookies
            self.api.session_data["token"] = self.ssid
            self.api.session_data["user_agent"] = self.headers["User-Agent"]
            output_file = Path(f"{self.api.resource_path}/session.json")
            output_file.parent.mkdir(exist_ok=True, parents=True)
            output_file.write_text(
                json.dumps({
                    "cookies": self.cookies,
                    "token": self.ssid,
                    "user_agent": self.headers["User-Agent"]
                }, indent=4)
            )
            return self.response, json.loads(match)

        return None, None

    def _get(self):
        return self.send_request(
            method="GET",
            url=f"f{self.full_url}/trade"
        )

    async def _post(self, data):
        """Send get request for Quotex API login http resource.
        :returns: The instance of :class:`requests.Response`.
        """
        print(f"Posting login data to: {self.full_url}/sign-in/")
        print(f"Data keys: {list(data.keys())}")

        self.response = self.send_request(
            method="POST",
            url=f"{self.full_url}/sign-in/",
            data=data
        )

        print(f"POST response status: {self.response.status_code}")
        print(f"POST response URL: {self.response.url}")

        required_keep_code = self.get_soup().find(
            "input", {"name": "keep_code"}
        )
        if required_keep_code:
            print("2FA code required")
            auth_body = self.get_soup().find(
                "main", {"class": "auth__body"}
            )
            input_message = (
                f'{auth_body.find("p").text}: ' if auth_body.find("p")
                else "Insira o código PIN que acabamos "
                     "de enviar para o seu e-mail: "
            )
            await self.awaiting_pin(data, input_message)

        await asyncio.sleep(1)
        print("Checking login success...")
        success = self.success_login()
        print(f"Login result: {success}")
        return success

    def success_login(self):
        if "trade" in self.response.url:
            return True, "Login successful."

        # Debug: Print response details
        print(f"Response URL: {self.response.url}")
        print(f"Response Status: {self.response.status_code}")

        html = self.get_soup()

        # Look for various error message containers
        error_selectors = [
            {"class": "hint--danger"},
            {"class": "input-control-cabinet__hint"},
            {"class": "alert-danger"},
            {"class": "error-message"},
            {"class": "auth-error"},
            {"class": "form-error"}
        ]

        error_message = ""
        for selector in error_selectors:
            match = html.find("div", selector)
            if match:
                error_message = match.text.strip()
                break

        # If no specific error found, check for general form validation
        if not error_message:
            # Check for any text that might indicate an error
            page_text = html.get_text().lower()
            if "invalid" in page_text:
                error_message = "Invalid credentials"
            elif "incorrect" in page_text:
                error_message = "Incorrect login information"
            elif "blocked" in page_text:
                error_message = "Account blocked"
            elif "suspended" in page_text:
                error_message = "Account suspended"
            else:
                # Save the HTML for debugging
                with open("login_response.html", "w", encoding="utf-8") as f:
                    f.write(str(html))
                error_message = "Unknown error - check login_response.html"

        return False, f"Login failed. {error_message}"

    async def __call__(self, username, password, user_data_dir=None):
        """Method to get Quotex API login http request.
        :param str username: The username of a Quotex server.
        :param str password: The password of a Quotex server.
        :param str user_data_dir: The optional value for path userdata.
        :returns: The instance of :class:`requests.Response`.
        """
        print(f"=== LOGIN METHOD CALLED ===")
        print(f"Username: {username}")
        print(f"Password: {'*' * len(password) if password else 'None'}")

        print("Getting login token...")
        token = self.get_token()
        print(f"Token retrieved: {'Yes' if token else 'No'}")
        print(f"Token value: {token[:20] if token else 'None'}...")

        if not token:
            return False, "Failed to get login token"

        data = {
            "_token": token,
            "email": username,
            "password": password,
            "remember": 1,
        }

        print(f"Attempting login for: {username}")
        try:
            status, msg = await self._post(data)
            print(f"Post result: status={status}, msg='{msg}'")
        except Exception as e:
            print(f"Exception in _post: {e}")
            import traceback
            traceback.print_exc()
            return False, f"Login error: {e}"

        if not status:
            print(f"Login failed: {msg}")
            return status, msg

        print("Getting profile...")
        self.get_profile()

        return status, msg
