#!/usr/bin/env python3
"""
Quotex Candle Builder
Converts real-time tick data to OHLC candles for EURUSD_otc
"""

import asyncio
import json
import time
import re
from datetime import datetime
from playwright.async_api import async_playwright

class CandleBuilder:
    """Build OHLC candles from tick data"""
    
    def __init__(self, timeframe=60):
        self.timeframe = timeframe  # seconds
        self.current_candle = None
        self.completed_candles = []
        self.tick_count = 0
        
    def add_tick(self, timestamp, price):
        """Add a tick and update current candle"""
        self.tick_count += 1
        
        # Calculate candle start time (round down to timeframe)
        candle_start = int(timestamp // self.timeframe) * self.timeframe
        
        # If this is a new candle period
        if self.current_candle is None or self.current_candle['start_time'] != candle_start:
            # Save previous candle if it exists
            if self.current_candle is not None:
                self.completed_candles.append(self.current_candle)
                print(f"📊 Completed candle: O:{self.current_candle['open']:.5f} H:{self.current_candle['high']:.5f} L:{self.current_candle['low']:.5f} C:{self.current_candle['close']:.5f} ({self.current_candle['tick_count']} ticks)")
            
            # Start new candle
            self.current_candle = {
                'start_time': candle_start,
                'end_time': candle_start + self.timeframe,
                'open': price,
                'high': price,
                'low': price,
                'close': price,
                'tick_count': 1,
                'first_tick_time': timestamp,
                'last_tick_time': timestamp
            }
        else:
            # Update existing candle
            self.current_candle['high'] = max(self.current_candle['high'], price)
            self.current_candle['low'] = min(self.current_candle['low'], price)
            self.current_candle['close'] = price
            self.current_candle['tick_count'] += 1
            self.current_candle['last_tick_time'] = timestamp
    
    def get_last_candles(self, count=10):
        """Get the last N completed candles"""
        all_candles = self.completed_candles.copy()
        
        # Include current candle if it has enough data
        if self.current_candle and self.current_candle['tick_count'] >= 5:
            all_candles.append(self.current_candle)
        
        return all_candles[-count:] if all_candles else []

async def fetch_and_build_candles():
    """Fetch tick data and build candles"""
    
    print("🚀 Starting Quotex Candle Builder...")
    print("🎯 Building EURUSD_otc 1-minute candles from tick data")
    print("="*60)
    
    candle_builder = CandleBuilder(timeframe=60)  # 1-minute candles
    websocket_messages = []
    tick_data = []
    
    async with async_playwright() as p:
        print("🌐 Launching browser...")
        browser = await p.chromium.launch(headless=False)
        
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        page = await context.new_page()
        
        def handle_websocket(ws):
            print(f"🔌 WebSocket connected: {ws.url}")
            
            def on_framereceived(payload):
                try:
                    if isinstance(payload, bytes):
                        message = payload.decode('utf-8', errors='ignore')
                    else:
                        message = str(payload)
                    
                    websocket_messages.append({
                        'timestamp': time.time(),
                        'data': message
                    })
                    
                    # Look for EURUSD tick data: ["EURUSD_otc", timestamp, price, direction]
                    if 'EURUSD_otc' in message and re.search(r'\d+\.\d{5}', message):
                        # Extract tick data
                        try:
                            # Parse the message format: ♦[["EURUSD_otc",1752336314.891,1.18294,1]]
                            if '["EURUSD_otc",' in message:
                                # Extract the array part
                                match = re.search(r'\["EURUSD_otc",([0-9.]+),([0-9.]+),([01])\]', message)
                                if match:
                                    timestamp = float(match.group(1))
                                    price = float(match.group(2))
                                    direction = int(match.group(3))
                                    
                                    # Store tick data
                                    tick_data.append({
                                        'timestamp': timestamp,
                                        'price': price,
                                        'direction': direction,
                                        'received_at': time.time()
                                    })
                                    
                                    # Add to candle builder
                                    candle_builder.add_tick(timestamp, price)
                                    
                                    # Show tick info
                                    direction_text = "↑" if direction == 1 else "↓"
                                    print(f"💹 Tick: {price:.5f} {direction_text} (Total ticks: {candle_builder.tick_count})")
                        
                        except Exception as e:
                            print(f"⚠️ Tick parsing error: {e}")
                
                except Exception as e:
                    print(f"⚠️ Message processing error: {e}")
            
            ws.on("framereceived", on_framereceived)
        
        page.on("websocket", handle_websocket)
        
        try:
            print("🌐 Opening Quotex...")
            await page.goto("https://market-qx.pro/pt/trade", timeout=60000)
            await page.wait_for_load_state("networkidle", timeout=30000)
            print("✅ Page loaded")
            
            # Check for login
            await asyncio.sleep(3)
            email_input = await page.query_selector('input[name="email"], input[type="email"]')
            
            if email_input and await email_input.is_visible():
                print("🔐 Performing login...")
                await email_input.fill("<EMAIL>")
                await asyncio.sleep(1)
                
                password_input = await page.query_selector('input[name="password"], input[type="password"]')
                if password_input:
                    await password_input.fill("Uz2309##2309")
                    await asyncio.sleep(1)
                    await password_input.press('Enter')
                    
                    await page.wait_for_load_state("networkidle", timeout=30000)
                    await asyncio.sleep(5)
                    print("✅ Login completed")
            else:
                print("✅ Already logged in")
            
            # Wait for WebSocket
            print("⏳ Waiting for WebSocket connection...")
            await asyncio.sleep(10)
            
            # Try to select EURUSD
            print("🎯 Selecting EURUSD...")
            eurusd_selectors = [
                'text=EURUSD',
                'text=EUR/USD',
                '[data-asset="EURUSD_otc"]',
                '.asset-item:has-text("EUR")'
            ]
            
            for selector in eurusd_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        await element.click()
                        print(f"✅ Selected EURUSD: {selector}")
                        await asyncio.sleep(3)
                        break
                except:
                    continue
            
            # Collect data for 60 seconds to build multiple candles
            print("📊 Collecting tick data for 60 seconds...")
            
            for i in range(60):
                current_candles = candle_builder.get_last_candles(5)
                print(f"⏳ {i+1}/60 - Ticks: {len(tick_data)}, Completed candles: {len(candle_builder.completed_candles)}")
                
                # Show current candle progress
                if candle_builder.current_candle:
                    current = candle_builder.current_candle
                    print(f"   📈 Current candle: O:{current['open']:.5f} H:{current['high']:.5f} L:{current['low']:.5f} C:{current['close']:.5f} ({current['tick_count']} ticks)")
                
                await asyncio.sleep(1)
            
            # Final results
            print("\n" + "="*60)
            print("📊 FINAL CANDLE RESULTS")
            print("="*60)
            
            final_candles = candle_builder.get_last_candles(10)
            
            print(f"📨 Total WebSocket messages: {len(websocket_messages)}")
            print(f"💹 Total ticks processed: {len(tick_data)}")
            print(f"📊 Completed candles: {len(candle_builder.completed_candles)}")
            print(f"📈 Current candle ticks: {candle_builder.current_candle['tick_count'] if candle_builder.current_candle else 0}")
            
            if final_candles:
                print(f"\n🎯 EURUSD_otc Last {len(final_candles)} Candles:")
                print("    #  Time     |   Open    |   High    |   Low     |   Close   | Ticks")
                print("   " + "-" * 70)
                
                for i, candle in enumerate(final_candles, 1):
                    candle_time = datetime.fromtimestamp(candle['start_time']).strftime('%H:%M:%S')
                    print(f"   {i:2d}. {candle_time} | {candle['open']:9.5f} | {candle['high']:9.5f} | {candle['low']:9.5f} | {candle['close']:9.5f} | {candle['tick_count']:5d}")
            else:
                print("❌ No candles built")
            
            # Save comprehensive data
            output_data = {
                'timestamp': time.time(),
                'datetime': datetime.now().isoformat(),
                'summary': {
                    'total_ticks': len(tick_data),
                    'completed_candles': len(candle_builder.completed_candles),
                    'websocket_messages': len(websocket_messages)
                },
                'candles': final_candles,
                'tick_data_sample': tick_data[-20:],  # Last 20 ticks
                'current_candle': candle_builder.current_candle
            }
            
            with open('quotex_candles_built.json', 'w') as f:
                json.dump(output_data, f, indent=2, default=str)
            
            print(f"\n💾 Complete data saved to quotex_candles_built.json")
            
            return final_candles
            
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
            return []
        
        finally:
            await browser.close()

if __name__ == "__main__":
    print("📊 Quotex Candle Builder")
    print("Converting tick data to OHLC candles...")
    print("="*60)
    
    result = asyncio.run(fetch_and_build_candles())
    
    if result:
        print(f"\n✅ SUCCESS! Built {len(result)} candles from live tick data")
        print("\n🎯 Integration ready:")
        print("1. ✅ WebSocket connection established")
        print("2. ✅ Real-time EURUSD_otc tick data captured")
        print("3. ✅ OHLC candles built from ticks")
        print("4. ✅ Ready for trading bot integration")
    else:
        print(f"\n⚠️ No candles built, check the data file for debugging")
    
    print("\n🚀 Your bot can now:")
    print("- Connect to Quotex WebSocket")
    print("- Receive real-time EURUSD_otc price data")
    print("- Build OHLC candles for technical analysis")
    print("- Process trading signals based on candle data")
