#!/usr/bin/env python3
"""
Quotex Live Candle Data Fetcher
Uses browser automation to establish WebSocket connection and fetch real-time candle data
"""

import asyncio
import json
import time
import re
from datetime import datetime
from playwright.async_api import async_playwright

class QuotexCandleFetcher:
    """Fetch live candle data from Quotex using browser automation"""
    
    def __init__(self, email, password, headless=False):
        self.email = email
        self.password = password
        self.headless = headless
        self.candle_data = {}
        self.websocket_messages = []
        self.price_updates = {}
        
    async def fetch_live_candles(self, assets=None, duration=30):
        """
        Fetch live candle data for specified assets
        
        Args:
            assets (list): List of assets to fetch (default: ['EURUSD_otc'])
            duration (int): How long to collect data in seconds
        
        Returns:
            dict: Candle data for each asset
        """
        if assets is None:
            assets = ['EURUSD_otc']
        
        async with async_playwright() as p:
            # Launch browser
            browser = await p.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security'
                ]
            )
            
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                viewport={'width': 1920, 'height': 1080}
            )
            
            page = await context.new_page()
            
            # Setup WebSocket message capture
            def handle_websocket(ws):
                print(f"🔌 WebSocket connected: {ws.url}")
                
                def on_framereceived(payload):
                    try:
                        self._process_websocket_message(payload)
                    except Exception as e:
                        print(f"⚠️ Message processing error: {e}")
                
                ws.on("framereceived", on_framereceived)
            
            page.on("websocket", handle_websocket)
            
            try:
                print("🌐 Opening Quotex trading page...")
                await page.goto("https://market-qx.pro/pt/trade", timeout=60000)
                
                # Wait for page to load
                await page.wait_for_load_state("networkidle", timeout=30000)
                await asyncio.sleep(3)
                
                # Check if login is needed
                try:
                    # Look for login form or modal
                    login_needed = await page.query_selector('input[name="email"], .modal-sign, .login-form')
                    
                    if login_needed:
                        print("🔐 Login required, authenticating...")
                        await self._perform_login(page)
                    else:
                        print("✅ Already logged in!")
                        
                except Exception as e:
                    print(f"⚠️ Login check error: {e}")
                
                # Wait for WebSocket connection to establish
                print("⏳ Waiting for WebSocket connection...")
                await asyncio.sleep(10)
                
                # Try to interact with trading interface to trigger data
                await self._interact_with_trading_interface(page, assets)
                
                # Collect data for specified duration
                print(f"📊 Collecting candle data for {duration} seconds...")
                start_time = time.time()
                
                while time.time() - start_time < duration:
                    # Display current data count
                    total_messages = len(self.websocket_messages)
                    candle_assets = len(self.candle_data)
                    
                    print(f"📈 Data collected: {total_messages} messages, {candle_assets} assets with candles")
                    
                    # Try to trigger more data by interacting with interface
                    if (time.time() - start_time) % 10 < 1:  # Every 10 seconds
                        await self._trigger_data_updates(page, assets)
                    
                    await asyncio.sleep(2)
                
                # Process and format final results
                results = self._format_candle_results()
                
                print(f"\n✅ Data collection completed!")
                print(f"📊 Final results: {len(results)} assets with candle data")
                
                return results
                
            except Exception as e:
                print(f"❌ Error during data collection: {e}")
                import traceback
                traceback.print_exc()
                return {}
            
            finally:
                await browser.close()
    
    async def _perform_login(self, page):
        """Perform login if needed"""
        try:
            # Try to find and fill email input
            email_selectors = [
                'input[name="email"]',
                'input[type="email"]',
                '.modal-sign__input-value[name="email"]',
                '#email'
            ]
            
            email_input = None
            for selector in email_selectors:
                try:
                    email_input = await page.wait_for_selector(selector, timeout=5000, state='visible')
                    if email_input:
                        print(f"✅ Found email input: {selector}")
                        break
                except:
                    continue
            
            if not email_input:
                print("⚠️ Could not find email input, trying to continue...")
                return
            
            # Fill email
            await email_input.fill(self.email)
            await asyncio.sleep(1)
            
            # Find password input
            password_selectors = [
                'input[name="password"]',
                'input[type="password"]',
                '.modal-sign__input-value[name="password"]',
                '#password'
            ]
            
            password_input = None
            for selector in password_selectors:
                try:
                    password_input = await page.wait_for_selector(selector, timeout=5000, state='visible')
                    if password_input:
                        print(f"✅ Found password input: {selector}")
                        break
                except:
                    continue
            
            if password_input:
                await password_input.fill(self.password)
                await asyncio.sleep(1)
                
                # Submit login
                await password_input.press('Enter')
                
                print("🚀 Login submitted, waiting for redirect...")
                await page.wait_for_load_state("networkidle", timeout=30000)
                await asyncio.sleep(5)
                
                print("✅ Login completed!")
            else:
                print("⚠️ Could not find password input")
                
        except Exception as e:
            print(f"⚠️ Login error: {e}")
    
    async def _interact_with_trading_interface(self, page, assets):
        """Interact with trading interface to trigger data"""
        try:
            print("🎯 Interacting with trading interface...")
            
            # Try to select different assets to trigger candle data
            for asset in assets:
                try:
                    # Look for asset selector
                    asset_selectors = [
                        f'[data-asset="{asset}"]',
                        f'[data-symbol="{asset}"]',
                        f'.asset-item:has-text("{asset.replace("_otc", "")}")',
                        f'.instrument-item:has-text("{asset.replace("_otc", "")}")'
                    ]
                    
                    for selector in asset_selectors:
                        try:
                            element = await page.query_selector(selector)
                            if element:
                                await element.click()
                                print(f"✅ Selected asset: {asset}")
                                await asyncio.sleep(2)
                                break
                        except:
                            continue
                    
                except Exception as e:
                    print(f"⚠️ Asset selection error for {asset}: {e}")
            
            # Try to interact with chart/timeframe selectors
            try:
                timeframe_selectors = [
                    '[data-period="60"]',
                    '.timeframe-1m',
                    '.chart-period-60'
                ]
                
                for selector in timeframe_selectors:
                    try:
                        element = await page.query_selector(selector)
                        if element:
                            await element.click()
                            print("✅ Selected 1-minute timeframe")
                            await asyncio.sleep(2)
                            break
                    except:
                        continue
                        
            except Exception as e:
                print(f"⚠️ Timeframe selection error: {e}")
                
        except Exception as e:
            print(f"⚠️ Interface interaction error: {e}")
    
    async def _trigger_data_updates(self, page, assets):
        """Trigger data updates by interacting with page"""
        try:
            # Try to refresh data by clicking on chart or asset list
            refresh_selectors = [
                '.chart-container',
                '.asset-list',
                '.trading-panel'
            ]
            
            for selector in refresh_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        await element.click()
                        await asyncio.sleep(1)
                        break
                except:
                    continue
                    
        except Exception as e:
            pass  # Ignore errors in data triggering
    
    def _process_websocket_message(self, payload):
        """Process incoming WebSocket messages"""
        try:
            # Convert payload to string if it's bytes
            if isinstance(payload, bytes):
                payload_str = payload.decode('utf-8', errors='ignore')
            else:
                payload_str = str(payload)
            
            # Store all messages
            self.websocket_messages.append({
                'timestamp': time.time(),
                'data': payload_str
            })
            
            # Look for candle data patterns
            if self._is_candle_data(payload_str):
                self._extract_candle_data(payload_str)
            
            # Look for price updates
            if self._is_price_update(payload_str):
                self._extract_price_update(payload_str)
                
        except Exception as e:
            print(f"⚠️ Message processing error: {e}")
    
    def _is_candle_data(self, message):
        """Check if message contains candle data"""
        candle_indicators = [
            'history',
            'candles',
            'ohlc',
            'EURUSD_otc',
            'GBPUSD_otc',
            'open',
            'high',
            'low',
            'close'
        ]
        
        message_lower = message.lower()
        return any(indicator in message_lower for indicator in candle_indicators)
    
    def _is_price_update(self, message):
        """Check if message contains price updates"""
        price_indicators = [
            'quotes',
            'price',
            'tick',
            'stream'
        ]
        
        message_lower = message.lower()
        return any(indicator in message_lower for indicator in price_indicators)
    
    def _extract_candle_data(self, message):
        """Extract candle data from message"""
        try:
            # Try to parse as JSON
            if message.startswith('42[') or message.startswith('43['):
                # Socket.IO message format
                json_part = message[2:]  # Remove Socket.IO prefix
                
                try:
                    data = json.loads(json_part)
                    
                    if isinstance(data, list) and len(data) > 1:
                        event_type = data[0]
                        event_data = data[1]
                        
                        # Look for candle data in various formats
                        if isinstance(event_data, dict):
                            self._process_candle_object(event_data)
                        elif isinstance(event_data, list):
                            self._process_candle_array(event_data)
                            
                except json.JSONDecodeError:
                    pass
            
            # Look for numeric patterns that might be OHLC data
            self._extract_numeric_patterns(message)
            
        except Exception as e:
            print(f"⚠️ Candle extraction error: {e}")
    
    def _process_candle_object(self, data):
        """Process candle data from object format"""
        try:
            # Look for common candle data fields
            if 'asset' in data or 'symbol' in data:
                asset = data.get('asset', data.get('symbol', 'unknown'))
                
                if asset not in self.candle_data:
                    self.candle_data[asset] = []
                
                # Extract OHLC data
                candle = {
                    'timestamp': data.get('time', data.get('timestamp', time.time())),
                    'open': data.get('open', data.get('o')),
                    'high': data.get('high', data.get('h', data.get('max'))),
                    'low': data.get('low', data.get('l', data.get('min'))),
                    'close': data.get('close', data.get('c')),
                    'volume': data.get('volume', data.get('v', 0))
                }
                
                # Only add if we have valid OHLC data
                if all(candle[key] is not None for key in ['open', 'high', 'low', 'close']):
                    self.candle_data[asset].append(candle)
                    print(f"📊 New candle for {asset}: O:{candle['open']} H:{candle['high']} L:{candle['low']} C:{candle['close']}")
                    
        except Exception as e:
            print(f"⚠️ Candle object processing error: {e}")
    
    def _process_candle_array(self, data):
        """Process candle data from array format"""
        try:
            # Look for arrays that might contain OHLC data
            for item in data:
                if isinstance(item, list) and len(item) >= 5:
                    # Might be [timestamp, open, high, low, close, volume]
                    try:
                        candle = {
                            'timestamp': float(item[0]),
                            'open': float(item[1]),
                            'high': float(item[2]),
                            'low': float(item[3]),
                            'close': float(item[4]),
                            'volume': float(item[5]) if len(item) > 5 else 0
                        }
                        
                        # Add to generic asset if we don't know which one
                        asset = 'EURUSD_otc'  # Default asset
                        if asset not in self.candle_data:
                            self.candle_data[asset] = []
                        
                        self.candle_data[asset].append(candle)
                        print(f"📊 New candle array for {asset}: O:{candle['open']} H:{candle['high']} L:{candle['low']} C:{candle['close']}")
                        
                    except (ValueError, IndexError):
                        continue
                        
        except Exception as e:
            print(f"⚠️ Candle array processing error: {e}")
    
    def _extract_numeric_patterns(self, message):
        """Extract numeric patterns that might be price data"""
        try:
            # Look for patterns like: asset_name,timestamp,price,direction
            # or sequences of numbers that might be OHLC
            
            # Find floating point numbers in the message
            numbers = re.findall(r'\d+\.\d+', message)
            
            if len(numbers) >= 4:
                # Might be OHLC data
                try:
                    prices = [float(num) for num in numbers[:4]]
                    
                    # Basic validation - prices should be in reasonable range
                    if all(0.1 < price < 100 for price in prices):
                        candle = {
                            'timestamp': time.time(),
                            'open': prices[0],
                            'high': max(prices),
                            'low': min(prices),
                            'close': prices[-1],
                            'volume': 0
                        }
                        
                        asset = 'EURUSD_otc'  # Default
                        if asset not in self.candle_data:
                            self.candle_data[asset] = []
                        
                        # Only add if significantly different from last candle
                        if not self.candle_data[asset] or abs(self.candle_data[asset][-1]['close'] - candle['close']) > 0.00001:
                            self.candle_data[asset].append(candle)
                            print(f"📊 Extracted candle for {asset}: O:{candle['open']} H:{candle['high']} L:{candle['low']} C:{candle['close']}")
                            
                except ValueError:
                    pass
                    
        except Exception as e:
            pass  # Ignore errors in pattern extraction
    
    def _extract_price_update(self, message):
        """Extract real-time price updates"""
        try:
            # Look for current price information
            numbers = re.findall(r'\d+\.\d+', message)
            
            if numbers:
                price = float(numbers[0])
                
                # Store price update
                asset = 'EURUSD_otc'  # Default
                self.price_updates[asset] = {
                    'price': price,
                    'timestamp': time.time()
                }
                
        except Exception as e:
            pass
    
    def _format_candle_results(self):
        """Format final candle results"""
        results = {}
        
        for asset, candles in self.candle_data.items():
            if candles:
                # Sort by timestamp and get last 10
                sorted_candles = sorted(candles, key=lambda x: x['timestamp'])
                last_10 = sorted_candles[-10:]
                
                results[asset] = {
                    'asset': asset,
                    'total_candles': len(candles),
                    'last_10_candles': last_10,
                    'latest_price': self.price_updates.get(asset, {}).get('price'),
                    'data_quality': 'good' if len(candles) >= 5 else 'limited'
                }
        
        return results
    
    def save_results(self, results, filename="quotex_candle_data.json"):
        """Save results to file"""
        try:
            output_data = {
                'timestamp': time.time(),
                'datetime': datetime.now().isoformat(),
                'candle_data': results,
                'total_websocket_messages': len(self.websocket_messages),
                'raw_messages_sample': self.websocket_messages[-10:] if self.websocket_messages else []
            }
            
            with open(filename, 'w') as f:
                json.dump(output_data, f, indent=2, default=str)
            
            print(f"💾 Results saved to {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Error saving results: {e}")
            return False

# Test function
async def test_candle_fetcher():
    """Test the candle fetcher"""
    
    fetcher = QuotexCandleFetcher(
        email="<EMAIL>",
        password="Uz2309##2309",
        headless=False  # Set to True for headless mode
    )
    
    print("🚀 Starting Quotex Live Candle Data Fetcher...")
    print("="*60)
    
    # Fetch candle data for multiple assets
    assets_to_fetch = [
        'EURUSD_otc',
        'GBPUSD_otc',
        'USDJPY_otc'
    ]
    
    results = await fetcher.fetch_live_candles(
        assets=assets_to_fetch,
        duration=45  # Collect data for 45 seconds
    )
    
    # Display results
    print("\n" + "="*60)
    print("📊 CANDLE DATA RESULTS")
    print("="*60)
    
    for asset, data in results.items():
        print(f"\n🎯 {asset}:")
        print(f"   Total candles collected: {data['total_candles']}")
        print(f"   Data quality: {data['data_quality']}")
        print(f"   Latest price: {data.get('latest_price', 'N/A')}")
        
        print(f"   📈 Last 10 candles:")
        for i, candle in enumerate(data['last_10_candles'], 1):
            timestamp = datetime.fromtimestamp(candle['timestamp']).strftime('%H:%M:%S')
            print(f"      {i:2d}. {timestamp} | O:{candle['open']:8.5f} H:{candle['high']:8.5f} L:{candle['low']:8.5f} C:{candle['close']:8.5f}")
    
    # Save results
    fetcher.save_results(results)
    
    print(f"\n✅ Candle data collection completed!")
    print(f"📊 Total assets with data: {len(results)}")
    
    return results

if __name__ == "__main__":
    asyncio.run(test_candle_fetcher())
