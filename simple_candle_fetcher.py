#!/usr/bin/env python3
"""
Simple Quotex Candle Data Fetcher
Focused approach to fetch EURUSD_otc candle data using browser automation
"""

import asyncio
import json
import time
import re
from datetime import datetime
from playwright.async_api import async_playwright

async def fetch_quotex_candles():
    """Fetch live candle data from Quotex"""
    
    print("🚀 Starting Quotex Candle Data Fetcher...")
    print("🎯 Target: EURUSD_otc last 10 candles")
    print("="*50)
    
    candle_data = []
    websocket_messages = []
    
    async with async_playwright() as p:
        # Launch browser (non-headless to see what's happening)
        print("🌐 Launching browser...")
        browser = await p.chromium.launch(headless=False)
        
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        page = await context.new_page()
        
        # Capture WebSocket messages
        def handle_websocket(ws):
            print(f"🔌 WebSocket connected: {ws.url}")
            
            def on_framereceived(payload):
                try:
                    # Convert to string
                    if isinstance(payload, bytes):
                        message = payload.decode('utf-8', errors='ignore')
                    else:
                        message = str(payload)
                    
                    # Store message
                    websocket_messages.append({
                        'timestamp': time.time(),
                        'data': message
                    })
                    
                    # Look for candle data
                    if 'EURUSD' in message or 'candle' in message.lower() or 'history' in message.lower():
                        print(f"📊 Potential candle data: {message[:100]}...")
                        
                        # Try to extract numbers that might be OHLC
                        numbers = re.findall(r'\d+\.\d+', message)
                        if len(numbers) >= 4:
                            try:
                                # Assume format might be [timestamp, open, high, low, close]
                                candle = {
                                    'timestamp': time.time(),
                                    'open': float(numbers[0]),
                                    'high': float(numbers[1]),
                                    'low': float(numbers[2]),
                                    'close': float(numbers[3]),
                                    'raw_message': message[:200]
                                }
                                
                                # Basic validation
                                if 0.5 < candle['open'] < 5.0:  # EUR/USD range
                                    candle_data.append(candle)
                                    print(f"✅ Candle extracted: O:{candle['open']} H:{candle['high']} L:{candle['low']} C:{candle['close']}")
                                    
                            except ValueError:
                                pass
                    
                    # Show any message with numbers (potential price data)
                    if re.search(r'\d+\.\d{4,}', message):
                        print(f"💹 Price data: {message[:150]}...")
                
                except Exception as e:
                    print(f"⚠️ Message processing error: {e}")
            
            ws.on("framereceived", on_framereceived)
        
        page.on("websocket", handle_websocket)
        
        try:
            # Navigate to Quotex
            print("🌐 Opening Quotex...")
            await page.goto("https://market-qx.pro/pt/trade", timeout=60000)
            
            # Wait for page load
            await page.wait_for_load_state("networkidle", timeout=30000)
            print("✅ Page loaded")
            
            # Check if login is needed
            await asyncio.sleep(3)
            
            # Look for login elements
            email_input = await page.query_selector('input[name="email"], input[type="email"]')
            
            if email_input and await email_input.is_visible():
                print("🔐 Login required...")
                
                # Fill email
                await email_input.fill("<EMAIL>")
                await asyncio.sleep(1)
                
                # Fill password
                password_input = await page.query_selector('input[name="password"], input[type="password"]')
                if password_input:
                    await password_input.fill("Uz2309##2309")
                    await asyncio.sleep(1)
                    
                    # Submit
                    await password_input.press('Enter')
                    print("🚀 Login submitted...")
                    
                    # Wait for login
                    await page.wait_for_load_state("networkidle", timeout=30000)
                    await asyncio.sleep(5)
                    print("✅ Login completed")
            else:
                print("✅ Already logged in or no login needed")
            
            # Wait for WebSocket connection
            print("⏳ Waiting for WebSocket connection...")
            await asyncio.sleep(10)
            
            # Try to interact with EURUSD
            print("🎯 Looking for EURUSD asset...")
            
            # Try different selectors for EURUSD
            eurusd_selectors = [
                '[data-asset="EURUSD_otc"]',
                '[data-symbol="EURUSD_otc"]',
                'text=EURUSD',
                'text=EUR/USD',
                '.asset-item:has-text("EUR")',
                '.instrument:has-text("EUR")'
            ]
            
            for selector in eurusd_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        await element.click()
                        print(f"✅ Clicked EURUSD element: {selector}")
                        await asyncio.sleep(3)
                        break
                except:
                    continue
            
            # Try to find and click chart or timeframe elements
            print("📊 Interacting with chart...")
            
            chart_selectors = [
                '.chart-container',
                '.trading-chart',
                '[data-period="60"]',
                '.timeframe-1m'
            ]
            
            for selector in chart_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element:
                        await element.click()
                        print(f"✅ Clicked chart element: {selector}")
                        await asyncio.sleep(2)
                        break
                except:
                    continue
            
            # Collect data for 30 seconds
            print("📊 Collecting candle data for 30 seconds...")
            
            for i in range(30):
                print(f"⏳ {i+1}/30 - Messages: {len(websocket_messages)}, Candles: {len(candle_data)}")
                
                # Try to trigger more data every 5 seconds
                if i % 5 == 0:
                    try:
                        # Click somewhere to trigger updates
                        await page.click('body')
                        await asyncio.sleep(0.5)
                    except:
                        pass
                
                await asyncio.sleep(1)
            
            print("\n" + "="*50)
            print("📊 FINAL RESULTS")
            print("="*50)
            
            print(f"📨 Total WebSocket messages: {len(websocket_messages)}")
            print(f"📊 Extracted candles: {len(candle_data)}")
            
            if candle_data:
                print(f"\n🎯 EURUSD_otc Candles (last 10):")
                
                # Sort by timestamp and get last 10
                sorted_candles = sorted(candle_data, key=lambda x: x['timestamp'])[-10:]
                
                for i, candle in enumerate(sorted_candles, 1):
                    timestamp = datetime.fromtimestamp(candle['timestamp']).strftime('%H:%M:%S')
                    print(f"   {i:2d}. {timestamp} | O:{candle['open']:7.5f} H:{candle['high']:7.5f} L:{candle['low']:7.5f} C:{candle['close']:7.5f}")
            else:
                print("❌ No candle data extracted")
                
                # Show sample messages for debugging
                print(f"\n🔍 Sample WebSocket messages:")
                for i, msg in enumerate(websocket_messages[-5:], 1):
                    print(f"   {i}. {msg['data'][:100]}...")
            
            # Save data
            output_data = {
                'timestamp': time.time(),
                'datetime': datetime.now().isoformat(),
                'candle_data': candle_data,
                'websocket_messages_count': len(websocket_messages),
                'sample_messages': [msg['data'][:200] for msg in websocket_messages[-10:]]
            }
            
            with open('quotex_candles_simple.json', 'w') as f:
                json.dump(output_data, f, indent=2, default=str)
            
            print(f"\n💾 Data saved to quotex_candles_simple.json")
            
            return candle_data
            
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
            return []
        
        finally:
            await browser.close()

if __name__ == "__main__":
    print("🎯 Simple Quotex Candle Fetcher")
    print("Fetching EURUSD_otc candle data...")
    print("="*50)
    
    result = asyncio.run(fetch_quotex_candles())
    
    if result:
        print(f"\n✅ SUCCESS! Fetched {len(result)} candles")
    else:
        print(f"\n⚠️ No candles fetched, but WebSocket connection was established")
    
    print("\n🎯 Next steps:")
    print("1. Check the saved JSON file for raw WebSocket data")
    print("2. Analyze message patterns to improve candle extraction")
    print("3. Integrate with your trading bot")
