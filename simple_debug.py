#!/usr/bin/env python3
"""Simple debug script to check file content"""

# Read the login.py file directly
with open('pyquotex/http/login.py', 'r') as f:
    lines = f.readlines()
    for i, line in enumerate(lines[10:20], 11):
        print(f"{i:2d}: {line.rstrip()}")

print("\n" + "="*50)

# Read the stable_api.py file directly  
with open('pyquotex/stable_api.py', 'r') as f:
    lines = f.readlines()
    for i, line in enumerate(lines[215:225], 216):
        print(f"{i:2d}: {line.rstrip()}")
