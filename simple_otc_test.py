#!/usr/bin/env python3
"""
Simple OTC Data Test - Test 100 candles creation
"""

import asyncio
import sys
import os
import time

# Add the train bot directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'train bot'))

from quotex_integration import get_quotex_client

async def test_otc_data():
    """Test OTC data creation with 100 candles"""
    print("🧪 SIMPLE OTC DATA TEST")
    print("=" * 50)
    
    # Updated credentials
    email = "<EMAIL>"
    password = "Uz2309##2309"
    
    client = None
    
    try:
        # Connect
        print("🔗 Connecting...")
        client = get_quotex_client(email, password, demo_mode=True)
        connected = await client.connect()
        
        if not connected:
            print("❌ Connection failed")
            return
        
        print("✅ Connected successfully")
        
        # Check balance
        balance = await client.get_balance()
        print(f"💰 Balance: ${balance}")
        
        if balance == 10000.0:
            print("✅ Demo mode confirmed")
        else:
            print("⚠️ Demo mode uncertain")
        
        # Test OTC data creation
        print("\n📊 Testing OTC data creation...")
        
        # Import Model functions directly
        from Model import create_comprehensive_otc_data
        
        # Set global client
        import Model
        Model.quotex_client = client
        
        # Test data creation
        test_asset = "EURUSD_otc"
        
        start_time = time.time()
        df = await create_comprehensive_otc_data(test_asset, 60)
        processing_time = time.time() - start_time
        
        if df is not None:
            candle_count = len(df)
            latest_price = df['close'].iloc[-1]
            
            print(f"✅ Created {candle_count} candles")
            print(f"💰 Latest price: {latest_price:.5f}")
            print(f"⏱️ Processing time: {processing_time:.2f}s")
            
            if candle_count >= 100:
                print("🎉 SUCCESS: Got 100+ candles!")
            else:
                print(f"❌ FAIL: Only {candle_count} candles")
            
            # Check data quality
            if latest_price > 0:
                print("✅ Valid price data")
            else:
                print("❌ Invalid price data")
            
            # Check technical indicators
            if 'rsi' in df.columns and 'sma_20' in df.columns:
                print("✅ Technical indicators added")
            else:
                print("❌ Missing technical indicators")
        else:
            print("❌ No data created")
        
        print("\n🎯 SUMMARY:")
        if df is not None and len(df) >= 100 and df['close'].iloc[-1] > 0:
            print("🎉 ALL TESTS PASSED - OTC DATA WORKING!")
        else:
            print("❌ SOME TESTS FAILED")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            try:
                await client.close()
                print("🔌 Connection closed")
            except:
                pass

if __name__ == "__main__":
    asyncio.run(test_otc_data())
