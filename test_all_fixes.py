#!/usr/bin/env python3
"""
Test All Critical Fixes for PyQuotex Trading Bot
Tests:
1. OTC data quantity (100 candles like live pairs)
2. Demo mode switching (proper detection)
3. Asset selection (enhanced selectors)
4. Trade execution (comprehensive UI detection)
5. Processing time (fast signal generation)
"""

import asyncio
import sys
import os
import time

# Add the train bot directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'train bot'))

from quotex_integration import get_quotex_client
from Model import fetch_quotex_market_data, generate_signal
from strategy_engine import StrategyEngine

async def test_all_critical_fixes():
    """Test all critical fixes"""
    print("🧪 TESTING ALL CRITICAL FIXES")
    print("=" * 80)
    
    # Updated credentials
    email = "<EMAIL>"
    password = "Uz2309##2309"
    
    client = None
    
    try:
        # Test 1: Connection and Demo Mode
        print("\n🔧 TEST 1: CONNECTION & DEMO MODE")
        print("-" * 50)
        
        client = get_quotex_client(email, password, demo_mode=True)
        connected = await client.connect()
        
        if not connected:
            print("❌ Connection failed")
            return
        
        print("✅ Connection successful")
        
        # Verify demo mode
        balance = await client.get_balance()
        if balance == 10000.0:
            print("✅ Demo mode confirmed (balance: $10,000)")
        else:
            print(f"⚠️ Demo mode uncertain (balance: ${balance})")
        
        # Test 2: OTC Data Quantity
        print("\n📊 TEST 2: OTC DATA QUANTITY (100 CANDLES)")
        print("-" * 50)
        
        test_asset = "EURUSD_otc"
        
        # Set global client for Model.py
        import train_bot.Model as Model
        Model.quotex_client = client
        
        start_time = time.time()
        df = await fetch_quotex_market_data(test_asset, "M1")
        processing_time = time.time() - start_time
        
        if df is not None:
            candle_count = len(df)
            print(f"✅ Retrieved {candle_count} candles for {test_asset}")
            print(f"⏱️ Processing time: {processing_time:.2f} seconds")
            
            if candle_count >= 100:
                print("✅ PASS: Got 100+ candles (same as live pairs)")
            else:
                print(f"❌ FAIL: Only got {candle_count} candles (need 100+)")
            
            # Check data quality
            latest_price = df['close'].iloc[-1]
            print(f"💰 Latest price: {latest_price:.5f}")
            
            if latest_price > 0:
                print("✅ PASS: Valid price data")
            else:
                print("❌ FAIL: Invalid price data")
        else:
            print("❌ FAIL: No data retrieved")
        
        # Test 3: Signal Generation Speed
        print("\n🎯 TEST 3: SIGNAL GENERATION SPEED")
        print("-" * 50)
        
        strategy_engine = StrategyEngine()
        selected_strategies = ["S1"]
        
        start_time = time.time()
        signal, confidence, price, strategy = await generate_signal(
            test_asset, strategy_engine, selected_strategies, "M1", df
        )
        signal_time = time.time() - start_time
        
        print(f"📈 Signal: {signal}")
        print(f"🎯 Confidence: {confidence*100:.1f}%")
        print(f"💰 Price: {price:.5f}")
        print(f"🔧 Strategy: {strategy}")
        print(f"⏱️ Signal generation time: {signal_time:.2f} seconds")
        
        if signal_time < 2.0:
            print("✅ PASS: Signal generated in <2 seconds")
        else:
            print(f"❌ FAIL: Signal took {signal_time:.2f} seconds (need <2s)")
        
        # Test 4: Trade Execution Elements
        print("\n🚀 TEST 4: TRADE EXECUTION ELEMENTS")
        print("-" * 50)
        
        if signal in ["call", "put"]:
            print(f"🎯 Testing {signal.upper()} trade execution...")
            
            # Test trade execution
            success, result = await client.trade(signal, 10.0, test_asset, 60)
            
            if success:
                print("✅ PASS: Trade execution successful")
                print(f"📋 Result: {result}")
            else:
                print("❌ FAIL: Trade execution failed")
                print(f"📋 Error: {result}")
        else:
            print("⚠️ SKIP: No signal generated for trade test")
        
        # Test 5: Processing Time Summary
        print("\n⏱️ TEST 5: PROCESSING TIME SUMMARY")
        print("-" * 50)
        
        total_time = processing_time + signal_time
        print(f"📊 Data fetching: {processing_time:.2f}s")
        print(f"🎯 Signal generation: {signal_time:.2f}s")
        print(f"⏱️ Total processing: {total_time:.2f}s")
        
        if total_time < 3.0:
            print("✅ PASS: Total processing <3 seconds (good for 2s before candle)")
        else:
            print(f"❌ FAIL: Total processing {total_time:.2f}s (need <3s)")
        
        # Final Summary
        print("\n📋 FINAL TEST SUMMARY")
        print("-" * 50)
        
        tests_passed = 0
        total_tests = 5
        
        if connected:
            tests_passed += 1
            print("✅ Connection & Demo Mode: PASS")
        else:
            print("❌ Connection & Demo Mode: FAIL")
        
        if df is not None and len(df) >= 100:
            tests_passed += 1
            print("✅ OTC Data Quantity: PASS")
        else:
            print("❌ OTC Data Quantity: FAIL")
        
        if signal_time < 2.0:
            tests_passed += 1
            print("✅ Signal Generation Speed: PASS")
        else:
            print("❌ Signal Generation Speed: FAIL")
        
        if signal in ["call", "put"]:
            tests_passed += 1
            print("✅ Signal Quality: PASS")
        else:
            print("❌ Signal Quality: FAIL")
        
        if total_time < 3.0:
            tests_passed += 1
            print("✅ Total Processing Time: PASS")
        else:
            print("❌ Total Processing Time: FAIL")
        
        print(f"\n🎯 OVERALL RESULT: {tests_passed}/{total_tests} tests passed")
        
        if tests_passed == total_tests:
            print("🎉 ALL TESTS PASSED - BOT IS READY!")
        elif tests_passed >= 3:
            print("⚠️ MOST TESTS PASSED - BOT IS MOSTLY FUNCTIONAL")
        else:
            print("❌ MULTIPLE FAILURES - BOT NEEDS MORE FIXES")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            try:
                await client.close()
                print("🔌 Connection closed")
            except:
                pass

if __name__ == "__main__":
    asyncio.run(test_all_critical_fixes())
