#!/usr/bin/env python3
"""
Test Bot Demo - Run the bot in demo mode to verify all fixes
"""

import asyncio
import sys
import os
import time

# Add the train bot directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'train bot'))

from Model import run_trading_bot

async def test_bot_demo():
    """Test the bot in demo mode"""
    print("🤖 TESTING BOT IN DEMO MODE")
    print("=" * 60)
    print("This will test:")
    print("✅ Silent data creation")
    print("✅ Clean signal display")
    print("✅ Trade execution messages")
    print("✅ Graceful shutdown")
    print("=" * 60)
    
    try:
        # Run the bot in practice mode for a short time
        await run_trading_bot("PRACTICE", is_practice_only=True)
        
    except KeyboardInterrupt:
        # Test graceful shutdown
        print("\n" + "=" * 80)
        print("🛑 Bot stopped by the owner <PERSON>")
        print("🎉 Hope your session was productive and successful!")
        print("💫 Thank you for using this Trading Model")
        print("=" * 80)
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("Press Ctrl+C after a few signals to test graceful shutdown")
    asyncio.run(test_bot_demo())
