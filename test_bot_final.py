#!/usr/bin/env python3
"""
Test Bot Final - Test the actual bot with all fixes
"""

import asyncio
import sys
import os
import signal

# Add the train bot directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'train bot'))

from utils import print_colored

def signal_handler(signum, frame):
    """Handle Ctrl+C gracefully"""
    print_colored("\n" + "=" * 80, "SKY_BLUE")
    print_colored("🛑 Bot stopped by the owner <PERSON>", "WARNING", bold=True)
    print_colored("🎉 Hope your session was productive and successful!", "SUCCESS", bold=True)
    print_colored("💫 Thank you for using this Trading Model", "SKY_BLUE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    sys.exit(0)

async def test_bot_final():
    """Test the bot with all fixes"""
    print_colored("🤖 TESTING BOT WITH ALL FIXES", "TITLE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    print_colored("This test will demonstrate:", "INFO")
    print_colored("✅ Correct asset selection before trading", "SUCCESS")
    print_colored("✅ Trade messages outside signal box", "SUCCESS")
    print_colored("✅ Optimized processing speed", "SUCCESS")
    print_colored("✅ Graceful shutdown on Ctrl+C", "SUCCESS")
    print_colored("=" * 80, "SKY_BLUE")
    
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # Import and run the bot
        from Model import run_trading_bot
        
        print_colored("\n🚀 Starting bot in demo mode...", "INFO")
        print_colored("Press Ctrl+C to test graceful shutdown", "WARNING")
        
        # Run the bot in practice mode
        await run_trading_bot("PRACTICE", is_practice_only=True)
        
    except KeyboardInterrupt:
        # This should be handled by the signal handler
        pass
    except Exception as e:
        print_colored(f"❌ Error: {e}", "ERROR")

if __name__ == "__main__":
    print_colored("🎯 FINAL BOT TEST - ALL FIXES IMPLEMENTED", "TITLE", bold=True)
    print_colored("Press Ctrl+C after seeing some signals to test shutdown", "WARNING")
    
    try:
        asyncio.run(test_bot_final())
    except KeyboardInterrupt:
        # Final fallback
        print_colored("\n" + "=" * 80, "SKY_BLUE")
        print_colored("🛑 Bot stopped by the owner Muhammad Uzair", "WARNING", bold=True)
        print_colored("🎉 Hope your session was productive and successful!", "SUCCESS", bold=True)
        print_colored("💫 Thank you for using this Trading Model", "SKY_BLUE", bold=True)
        print_colored("=" * 80, "SKY_BLUE")
