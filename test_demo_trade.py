#!/usr/bin/env python3
"""
Test Demo Trading with PyQuotex Integration
This script will test placing a manual trade on the demo account
"""

import asyncio
import sys
import os

# Add the train bot directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'train bot'))

from quotex_integration import get_quotex_client

async def test_demo_trading():
    """Test demo trading functionality"""
    print("🧪 Testing Demo Trading with PyQuotex Integration")
    print("=" * 60)
    
    # Updated credentials
    email = "<EMAIL>"
    password = "Uz2309##2309"
    
    try:
        # Create client instance for demo trading
        print("🔗 Creating Quotex client...")
        client = get_quotex_client(email, password, demo_mode=True)
        
        # Connect to Quotex
        print("🔐 Connecting to Quotex...")
        connected = await client.connect()
        
        if not connected:
            print("❌ Failed to connect to Quotex")
            return
        
        print("✅ Connected to Quotex successfully!")
        
        # Set to demo account
        client.change_account("PRACTICE")
        print("🔵 Switched to Demo account")
        
        # Check balance
        print("💰 Checking demo balance...")
        balance = await client.get_balance()
        print(f"💰 Demo Balance: ${balance:.2f}")
        
        if balance <= 0:
            print("❌ Insufficient demo balance")
            return
        
        # Test asset availability
        test_asset = "EURUSD_otc"
        print(f"🔍 Checking asset availability: {test_asset}")
        asset_info = client.check_asset_open(test_asset)
        print(f"📊 Asset info: {asset_info}")
        
        # Create a test signal
        print("\n🎯 Creating test trading signal...")
        test_signal = {
            'asset': test_asset,
            'action': 'call',  # or 'put'
            'amount': 10.0,
            'duration': 60,  # 1 minute
            'confidence': 0.75
        }
        
        print(f"📈 Test Signal: {test_signal['action'].upper()} {test_signal['asset']} ${test_signal['amount']} for {test_signal['duration']}s")
        print(f"🎯 Confidence: {test_signal['confidence']*100:.1f}%")
        
        # Attempt to place the trade
        print("\n🚀 Attempting to place demo trade...")
        success, trade_info = await client.trade(
            test_signal['action'], 
            test_signal['amount'], 
            test_signal['asset'], 
            test_signal['duration']
        )
        
        if success:
            print("✅ Trade placed successfully!")
            print(f"📋 Trade info: {trade_info}")
            
            # Check balance after trade
            new_balance = await client.get_balance()
            print(f"💰 Balance after trade: ${new_balance:.2f}")
            
        else:
            print("❌ Trade failed")
            print(f"📋 Error info: {trade_info}")
            
            # Show manual trading instructions
            if isinstance(trade_info, dict) and 'manual_steps' in trade_info:
                print("\n📋 Manual Trading Instructions:")
                for step in trade_info['manual_steps']:
                    print(f"   {step}")
                print(f"\n🌐 Trading URL: {trade_info['url']}")
        
        print("\n✅ Demo trading test completed!")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_demo_trading())
