#!/usr/bin/env python3
"""
Test PyQuotex Direct API Trading
"""

import asyncio
import sys
import os
import time

# Add the train bot directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'train bot'))

from quotex_integration import get_quotex_client

async def test_direct_api():
    """Test PyQuotex direct API trading"""
    print("🔥 TESTING PYQUOTEX DIRECT API TRADING")
    print("=" * 60)
    
    # Updated credentials
    email = "<EMAIL>"
    password = "Uz2309##2309"
    
    client = None
    
    try:
        # Connect
        print("🔗 Connecting...")
        client = get_quotex_client(email, password, demo_mode=True)
        connected = await client.connect()
        
        if not connected:
            print("❌ Connection failed")
            return
        
        print("✅ Connected successfully")
        
        # Check balance
        balance = await client.get_balance()
        print(f"💰 Balance: ${balance}")
        
        # Test direct API trading
        print("\n🔥 TESTING DIRECT API TRADING")
        print("-" * 40)
        
        # Test parameters
        amount = 10.0
        asset = "EURUSD"  # No _otc suffix for API
        direction = "call"
        duration = 60
        
        print(f"📊 Trade parameters:")
        print(f"   Amount: ${amount}")
        print(f"   Asset: {asset}")
        print(f"   Direction: {direction}")
        print(f"   Duration: {duration}s")
        
        # Execute direct API trade
        print(f"\n⚡ Executing direct API trade...")
        
        start_time = time.time()
        
        try:
            # Use PyQuotex's direct buy method
            success, result = await client.api.buy(amount, asset, direction, duration)
            execution_time = time.time() - start_time
            
            print(f"⏱️ Execution time: {execution_time:.2f}s")
            
            if success:
                print(f"🎉 DIRECT API TRADE SUCCESS!")
                print(f"📋 Result: {result}")
                
                if execution_time <= 1.0:
                    print("⚡ ULTRA-FAST: <1 second execution!")
                elif execution_time <= 2.0:
                    print("🚀 FAST: <2 second execution!")
                else:
                    print("⚠️ SLOW: >2 second execution")
            else:
                print(f"❌ DIRECT API TRADE FAILED")
                print(f"📋 Error: {result}")
                
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"❌ Direct API error: {e}")
            print(f"⏱️ Failed after: {execution_time:.2f}s")
        
        # Check balance after trade
        print(f"\n💰 Checking balance after trade...")
        try:
            new_balance = await client.get_balance()
            balance_change = balance - new_balance
            print(f"💰 New balance: ${new_balance}")
            print(f"💸 Balance change: ${balance_change}")
            
            if balance_change > 0:
                print("✅ Trade confirmed - balance was deducted!")
            else:
                print("⚠️ No balance change detected")
        except Exception as e:
            print(f"❌ Balance check error: {e}")
        
        # Summary
        print(f"\n📋 SUMMARY")
        print("-" * 40)
        print("✅ Connection: SUCCESS")
        print("✅ Balance Check: SUCCESS")
        print(f"{'✅' if 'success' in locals() and success else '❌'} Direct API Trade: {'SUCCESS' if 'success' in locals() and success else 'FAILED'}")
        
        if 'execution_time' in locals():
            if execution_time <= 1.0:
                print("🎉 PERFORMANCE: ULTRA-FAST (<1s)")
            elif execution_time <= 2.0:
                print("✅ PERFORMANCE: FAST (<2s)")
            else:
                print("⚠️ PERFORMANCE: NEEDS IMPROVEMENT")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            try:
                await client.close()
                print("🔌 Connection closed")
            except:
                pass

if __name__ == "__main__":
    asyncio.run(test_direct_api())
