import asyncio
import logging
from pyquotex.stable_api import Quotex

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_with_dummy_credentials():
    client = Quotex(
        email="<EMAIL>",  # Dummy email
        password="wrongpassword",  # Dummy password
        lang="pt"
    )
    
    client.debug_ws_enable = True
    
    try:
        print("Testing with dummy credentials...")
        await client.connect()
        print("Connected successfully!")
        
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if client:
            await client.close()

async def test_with_real_credentials():
    client = Quotex(
        email="<EMAIL>",
        password="Uz2309##2309",
        lang="pt"
    )
    
    client.debug_ws_enable = True
    
    try:
        print("Testing with real credentials...")
        await client.connect()
        print("Connected successfully!")
        
        balance = await client.get_balance()
        print(f"Balance: {balance}")
        
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if client:
            await client.close()

async def main():
    print("=== Testing with dummy credentials ===")
    await test_with_dummy_credentials()
    
    print("\n=== Testing with real credentials ===")
    await test_with_real_credentials()

asyncio.run(main())
