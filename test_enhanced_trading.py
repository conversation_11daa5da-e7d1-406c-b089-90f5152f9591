#!/usr/bin/env python3
"""
Test Enhanced PyQuotex Integration with WebSocket and Automatic Trading
This script will test:
1. WebSocket connection establishment
2. Real-time OTC data fetching
3. Automatic trade execution on demo account
4. Balance verification after trade
"""

import asyncio
import sys
import os

# Add the train bot directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'train bot'))

from quotex_integration import get_quotex_client

async def test_enhanced_integration():
    """Test enhanced PyQuotex integration"""
    print("🧪 Testing Enhanced PyQuotex Integration")
    print("=" * 60)
    
    # Updated credentials
    email = "<EMAIL>"
    password = "Uz2309##2309"
    
    client = None
    
    try:
        # Create client instance for demo trading
        print("🔗 Creating enhanced Quotex client...")
        client = get_quotex_client(email, password, demo_mode=True)
        
        # Connect with WebSocket
        print("🔐 Connecting to Quotex with WebSocket...")
        connected = await client.connect()
        
        if not connected:
            print("❌ Failed to connect to Quotex")
            return
        
        print("✅ Connected to Quotex with WebSocket successfully!")
        
        # Check initial balance
        print("💰 Checking initial demo balance...")
        initial_balance = await client.get_balance()
        print(f"💰 Initial Demo Balance: ${initial_balance:.2f}")
        
        if initial_balance <= 0:
            print("❌ Insufficient demo balance")
            return
        
        # Test WebSocket data collection
        print("\n📊 Testing WebSocket data collection...")
        test_asset = "EURUSD_otc"
        
        # Wait for some tick data to accumulate
        print(f"⏳ Waiting 10 seconds for {test_asset} tick data...")
        await asyncio.sleep(10)
        
        # Check if we have tick data
        if test_asset in client.tick_data and len(client.tick_data[test_asset]) > 0:
            tick_count = len(client.tick_data[test_asset])
            latest_price = client.current_prices.get(test_asset, 0)
            print(f"✅ Collected {tick_count} ticks for {test_asset}")
            print(f"📈 Latest price: {latest_price}")
        else:
            print(f"⚠️ No tick data collected for {test_asset} yet")
        
        # Test candle data fetching
        print(f"\n📊 Testing candle data fetching for {test_asset}...")
        candles = await client.get_candles_browser(test_asset, period=60, count=10)
        
        if candles and len(candles) > 0:
            print(f"✅ Retrieved {len(candles)} candles")
            latest_candle = candles[-1]
            print(f"📊 Latest candle: O:{latest_candle['open']:.5f} H:{latest_candle['high']:.5f} L:{latest_candle['low']:.5f} C:{latest_candle['close']:.5f}")
        else:
            print("❌ No candle data retrieved")
        
        # Test automatic trade execution
        print(f"\n🎯 Testing automatic trade execution...")
        
        # Create test trade
        test_trade = {
            'asset': test_asset,
            'action': 'call',  # CALL trade
            'amount': 10.0,    # $10 trade
            'duration': 60     # 1 minute
        }
        
        print(f"🚀 Executing test trade: {test_trade['action'].upper()} {test_trade['asset']} ${test_trade['amount']}")
        
        # Execute the trade
        success, trade_result = await client.trade(
            test_trade['action'],
            test_trade['amount'],
            test_trade['asset'],
            test_trade['duration']
        )
        
        if success:
            print("✅ Trade executed successfully!")
            print(f"📋 Trade result: {trade_result}")
            
            if isinstance(trade_result, dict) and 'balance_after' in trade_result:
                balance_before = trade_result['balance_before']
                balance_after = trade_result['balance_after']
                amount_deducted = trade_result['amount_deducted']
                
                print(f"💰 Balance before: ${balance_before:.2f}")
                print(f"💰 Balance after: ${balance_after:.2f}")
                print(f"💸 Amount deducted: ${amount_deducted:.2f}")
                
                if amount_deducted > 0:
                    print("✅ Trade confirmed - balance was deducted!")
                    print("🎉 Automatic trading is working perfectly!")
                else:
                    print("⚠️ Trade may not have been executed - no balance change")
            else:
                print("⚠️ Trade result format unexpected")
        else:
            print("❌ Trade execution failed")
            print(f"📋 Error: {trade_result}")
        
        # Final balance check
        print(f"\n💰 Final balance check...")
        final_balance = await client.get_balance()
        print(f"💰 Final Demo Balance: ${final_balance:.2f}")
        
        # Summary
        print(f"\n📊 Test Summary:")
        print(f"   Initial Balance: ${initial_balance:.2f}")
        print(f"   Final Balance: ${final_balance:.2f}")
        print(f"   Balance Change: ${initial_balance - final_balance:.2f}")
        
        if initial_balance > final_balance:
            print("✅ SUCCESS: Balance was reduced - trade was executed!")
        else:
            print("⚠️ WARNING: No balance change detected")
        
        print("\n✅ Enhanced integration test completed!")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        if client:
            try:
                await client.close()
                print("🔌 Connection closed")
            except:
                pass

if __name__ == "__main__":
    asyncio.run(test_enhanced_integration())
