#!/usr/bin/env python3
"""
Test Fast Trade Execution - Verify 1-2 second execution time
"""

import asyncio
import sys
import os
import time

# Add the train bot directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'train bot'))

from quotex_integration import get_quotex_client

async def test_fast_execution():
    """Test ultra-fast trade execution"""
    print("⚡ TESTING ULTRA-FAST TRADE EXECUTION")
    print("=" * 60)
    
    # Updated credentials
    email = "<EMAIL>"
    password = "Uz2309##2309"
    
    client = None
    
    try:
        # Phase 1: Quick Connection
        print("\n🔗 PHASE 1: QUICK CONNECTION")
        print("-" * 40)
        
        start_time = time.time()
        client = get_quotex_client(email, password, demo_mode=True)
        connected = await client.connect()
        connection_time = time.time() - start_time
        
        if not connected:
            print("❌ Connection failed")
            return
        
        print(f"✅ Connected in {connection_time:.2f}s")
        
        # Phase 2: Fast Demo Mode Check
        print("\n🔵 PHASE 2: FAST DEMO MODE CHECK")
        print("-" * 40)
        
        start_time = time.time()
        balance = await client.get_balance()
        demo_check_time = time.time() - start_time
        
        print(f"💰 Balance: ${balance} (checked in {demo_check_time:.2f}s)")
        
        if balance == 10000.0:
            print("✅ Demo mode confirmed instantly")
        else:
            print("⚠️ Demo mode uncertain")
        
        # Phase 3: Ultra-Fast Trade Execution Test
        print("\n⚡ PHASE 3: ULTRA-FAST TRADE EXECUTION")
        print("-" * 40)
        
        test_trades = [
            ("call", 10.0, "EURUSD_otc", 60),
            ("put", 10.0, "EURUSD_otc", 60)
        ]
        
        for action, amount, asset, duration in test_trades:
            print(f"\n🚀 Testing {action.upper()} trade...")
            
            start_time = time.time()
            success, result = await client.trade(action, amount, asset, duration)
            execution_time = time.time() - start_time
            
            print(f"⏱️ Execution time: {execution_time:.2f}s")
            
            if execution_time <= 2.0:
                print(f"✅ FAST EXECUTION: {execution_time:.2f}s ≤ 2.0s")
            else:
                print(f"❌ SLOW EXECUTION: {execution_time:.2f}s > 2.0s")
            
            if success:
                print(f"✅ Trade result: {result}")
            else:
                print(f"❌ Trade failed: {result}")
            
            # Wait between trades
            await asyncio.sleep(2)
        
        # Phase 4: Performance Summary
        print("\n📊 PHASE 4: PERFORMANCE SUMMARY")
        print("-" * 40)
        
        total_time = connection_time + demo_check_time
        print(f"🔗 Connection time: {connection_time:.2f}s")
        print(f"🔵 Demo check time: {demo_check_time:.2f}s")
        print(f"⏱️ Total setup time: {total_time:.2f}s")
        
        if total_time <= 5.0:
            print("✅ FAST SETUP: Ready for trading in <5s")
        else:
            print("❌ SLOW SETUP: Takes >5s to be ready")
        
        # Phase 5: Final Assessment
        print("\n🎯 PHASE 5: FINAL ASSESSMENT")
        print("-" * 40)
        
        if connection_time <= 3.0 and demo_check_time <= 1.0:
            print("🎉 EXCELLENT: Ultra-fast execution achieved!")
            print("✅ Ready for 1-2 second trade execution")
        elif connection_time <= 5.0 and demo_check_time <= 2.0:
            print("✅ GOOD: Fast execution achieved")
            print("⚠️ May need optimization for 1-2 second target")
        else:
            print("❌ NEEDS IMPROVEMENT: Execution too slow")
            print("🔧 Requires further optimization")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            try:
                await client.close()
                print("🔌 Connection closed")
            except:
                pass

if __name__ == "__main__":
    asyncio.run(test_fast_execution())
