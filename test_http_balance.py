import asyncio
import logging
from pyquotex.stable_api import Quotex

# Enable debug logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def get_balance_via_http():
    client = Quotex(
        email="<EMAIL>",
        password="Uz2309##2309",
        lang="pt"
    )
    
    try:
        print("Creating API connection...")
        # Create the API connection but don't start WebSocket
        client.api = client.api or type('obj', (object,), {
            'host': 'market-qx.pro',
            'https_url': 'https://market-qx.pro',
            'lang': 'pt',
            'session_data': client.session_data,
            'username': client.email,
            'password': client.password,
            'user_data_dir': client.user_data_dir,
            'resource_path': client.resource_path
        })()
        
        # Import the necessary classes
        from pyquotex.api import QuotexAPI
        
        # Create API instance
        api = QuotexAPI(
            "market-qx.pro",
            client.email,
            client.password,
            client.lang,
            resource_path=client.resource_path,
            user_data_dir=client.user_data_dir
        )
        api.session_data = client.session_data
        
        print("Authenticating...")
        # Only authenticate (login) without starting WebSocket
        await api.authenticate()
        
        print("Getting settings/balance via HTTP...")
        # Get settings which should include balance
        settings_data = api.settings.get_settings()
        
        print("Settings data received:")
        print(f"Response type: {type(settings_data)}")
        
        if isinstance(settings_data, dict):
            # Look for balance-related fields
            for key, value in settings_data.items():
                if 'balance' in key.lower() or 'money' in key.lower() or 'amount' in key.lower():
                    print(f"Balance field found: {key} = {value}")
                elif 'demo' in key.lower():
                    print(f"Demo field: {key} = {value}")
                elif 'real' in key.lower():
                    print(f"Real field: {key} = {value}")
            
            # Print all keys to see what's available
            print(f"\nAll available keys: {list(settings_data.keys())}")
            
            # Print the full response for debugging
            print(f"\nFull response: {settings_data}")
        else:
            print(f"Unexpected response format: {settings_data}")
            
        return True
        
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(get_balance_via_http())
    print(f"HTTP balance test result: {'SUCCESS' if success else 'FAILED'}")
