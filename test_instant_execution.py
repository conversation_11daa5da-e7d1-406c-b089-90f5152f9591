#!/usr/bin/env python3
"""
Test Instant Trade Execution - Verify <1 second execution
"""

import asyncio
import sys
import os
import time

# Add the train bot directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'train bot'))

from quotex_integration import get_quotex_client

async def test_instant_execution():
    """Test instant trade execution"""
    print("⚡ TESTING INSTANT TRADE EXECUTION")
    print("=" * 60)
    
    # Updated credentials
    email = "<EMAIL>"
    password = "Uz2309##2309"
    
    client = None
    
    try:
        # Phase 1: Connection (allow normal time)
        print("\n🔗 PHASE 1: CONNECTION")
        print("-" * 40)
        
        start_time = time.time()
        client = get_quotex_client(email, password, demo_mode=True)
        connected = await client.connect()
        connection_time = time.time() - start_time
        
        if not connected:
            print("❌ Connection failed")
            return
        
        print(f"✅ Connected in {connection_time:.2f}s")
        
        # Phase 2: Instant Demo Mode Check
        print("\n⚡ PHASE 2: INSTANT DEMO MODE")
        print("-" * 40)
        
        start_time = time.time()
        balance = await client.get_balance()
        demo_time = time.time() - start_time
        
        print(f"💰 Balance: ${balance} (checked in {demo_time:.2f}s)")
        
        # Phase 3: INSTANT Trade Execution Tests
        print("\n⚡ PHASE 3: INSTANT TRADE EXECUTION")
        print("-" * 40)
        
        test_trades = [
            ("call", 10.0, "EURUSD_otc", 60),
            ("put", 10.0, "EURUSD_otc", 60)
        ]
        
        execution_times = []
        
        for action, amount, asset, duration in test_trades:
            print(f"\n⚡ INSTANT {action.upper()} test...")
            
            start_time = time.time()
            success, result = await client.trade(action, amount, asset, duration)
            execution_time = time.time() - start_time
            
            execution_times.append(execution_time)
            
            print(f"⏱️ Execution time: {execution_time:.2f}s")
            
            if execution_time <= 1.0:
                print(f"🎉 INSTANT: {execution_time:.2f}s ≤ 1.0s")
            elif execution_time <= 2.0:
                print(f"✅ FAST: {execution_time:.2f}s ≤ 2.0s")
            else:
                print(f"❌ SLOW: {execution_time:.2f}s > 2.0s")
            
            if success:
                print(f"✅ Trade result: {result}")
            else:
                print(f"❌ Trade failed: {result}")
            
            # Short wait between trades
            await asyncio.sleep(1)
        
        # Phase 4: Performance Analysis
        print("\n📊 PHASE 4: PERFORMANCE ANALYSIS")
        print("-" * 40)
        
        avg_execution = sum(execution_times) / len(execution_times)
        min_execution = min(execution_times)
        max_execution = max(execution_times)
        
        print(f"⏱️ Average execution time: {avg_execution:.2f}s")
        print(f"⚡ Fastest execution: {min_execution:.2f}s")
        print(f"🐌 Slowest execution: {max_execution:.2f}s")
        
        # Performance rating
        if avg_execution <= 1.0:
            print("🎉 PERFORMANCE: INSTANT (<1s average)")
        elif avg_execution <= 2.0:
            print("✅ PERFORMANCE: FAST (<2s average)")
        elif avg_execution <= 5.0:
            print("⚠️ PERFORMANCE: ACCEPTABLE (<5s average)")
        else:
            print("❌ PERFORMANCE: TOO SLOW (>5s average)")
        
        # Phase 5: Final Assessment
        print("\n🎯 PHASE 5: FINAL ASSESSMENT")
        print("-" * 40)
        
        instant_trades = sum(1 for t in execution_times if t <= 1.0)
        fast_trades = sum(1 for t in execution_times if t <= 2.0)
        total_trades = len(execution_times)
        
        print(f"⚡ Instant trades (<1s): {instant_trades}/{total_trades}")
        print(f"🚀 Fast trades (<2s): {fast_trades}/{total_trades}")
        
        if instant_trades == total_trades:
            print("🎉 PERFECT: All trades executed instantly!")
        elif fast_trades == total_trades:
            print("✅ EXCELLENT: All trades executed fast!")
        elif fast_trades >= total_trades * 0.5:
            print("⚠️ GOOD: Most trades executed fast")
        else:
            print("❌ NEEDS IMPROVEMENT: Too many slow trades")
        
        # Ready for production?
        if avg_execution <= 2.0 and fast_trades >= total_trades * 0.8:
            print("\n🎉 READY FOR PRODUCTION!")
            print("✅ Bot can execute trades within 2-second window")
        else:
            print("\n⚠️ NEEDS OPTIMIZATION")
            print("🔧 Bot requires further speed improvements")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            try:
                await client.close()
                print("🔌 Connection closed")
            except:
                pass

if __name__ == "__main__":
    asyncio.run(test_instant_execution())
