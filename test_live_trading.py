#!/usr/bin/env python3
"""
Test Live Trading with PyQuotex
Tests live candle data fetching and trade placement
"""

import asyncio
import time
import logging
from pyquotex.stable_api import Quotex

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LiveTradingTest:
    def __init__(self, email, password, demo_mode=True):
        self.email = email
        self.password = password
        self.demo_mode = demo_mode
        self.client = None
        self.is_connected = False
        
    async def connect_with_retries(self, max_attempts=5):
        """Connect to Quotex with multiple retry attempts"""
        for attempt in range(1, max_attempts + 1):
            try:
                print(f"🔌 Connection attempt {attempt}/{max_attempts}")
                
                # Create new client for each attempt
                self.client = Quotex(
                    email=self.email,
                    password=self.password,
                    lang="pt"
                )
                
                # Enable debug mode
                self.client.debug_ws_enable = True
                
                # Attempt connection
                check_connect, message = await self.client.connect()
                
                if check_connect:
                    print("✅ Connected to Quotex successfully!")
                    self.is_connected = True
                    
                    # Set account mode
                    if self.demo_mode:
                        await self.client.change_account("PRACTICE")
                        print("🔵 Switched to Demo account")
                    else:
                        await self.client.change_account("REAL")
                        print("🔴 Switched to Live account")
                    
                    return True
                else:
                    print(f"❌ Connection failed: {message}")
                    if attempt < max_attempts:
                        print(f"⏳ Waiting 10 seconds before retry...")
                        await asyncio.sleep(10)
                    
            except Exception as e:
                print(f"❌ Connection error: {e}")
                if attempt < max_attempts:
                    print(f"⏳ Waiting 10 seconds before retry...")
                    await asyncio.sleep(10)
        
        print("💥 Failed to connect after all attempts")
        return False
    
    async def test_balance(self):
        """Test balance retrieval"""
        try:
            if not self.is_connected:
                return False
            
            print("\n💰 Testing balance retrieval...")
            balance = await self.client.get_balance()
            
            if balance is not None:
                print(f"✅ Current balance: ${balance:.2f}")
                return True
            else:
                print("❌ Failed to get balance")
                return False
                
        except Exception as e:
            print(f"❌ Balance test error: {e}")
            return False
    
    async def test_candle_data(self, asset="EURUSD_otc", period=60, count=10):
        """Test live candle data fetching"""
        try:
            if not self.is_connected:
                return False
            
            print(f"\n📊 Testing candle data for {asset}...")
            print(f"   Period: {period}s, Count: {count}")
            
            # Get historical candles
            end_time = time.time()
            offset = count * period
            
            candles = await self.client.get_candles(asset, end_time, offset, period)
            
            if candles and len(candles) > 0:
                print(f"✅ Retrieved {len(candles)} candles")
                
                # Display latest candles
                print(f"📈 Latest candles for {asset}:")
                for i, candle in enumerate(candles[-3:]):  # Show last 3 candles
                    timestamp = candle.get('time', 0)
                    open_price = candle.get('open', 0)
                    high_price = candle.get('max', 0)
                    low_price = candle.get('min', 0)
                    close_price = candle.get('close', 0)
                    
                    print(f"   {i+1}. Time: {timestamp}, O: {open_price}, H: {high_price}, L: {low_price}, C: {close_price}")
                
                return True
            else:
                print("❌ No candle data retrieved")
                return False
                
        except Exception as e:
            print(f"❌ Candle data test error: {e}")
            return False
    
    async def test_real_time_data(self, asset="EURUSD_otc", duration=15):
        """Test real-time price data"""
        try:
            if not self.is_connected:
                return False
            
            print(f"\n📡 Testing real-time data for {asset} ({duration}s)...")
            
            # Start candle stream
            self.client.start_candles_stream(asset, 60)
            
            print("⏳ Collecting real-time data...")
            start_time = time.time()
            
            while time.time() - start_time < duration:
                try:
                    # Get real-time price
                    price_data = await self.client.start_realtime_price(asset, 60)
                    
                    if price_data and asset in price_data:
                        current_price = price_data[asset]
                        print(f"💹 {asset}: {current_price}")
                    
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    print(f"⚠️ Real-time data error: {e}")
                    break
            
            print("✅ Real-time data test completed")
            return True
            
        except Exception as e:
            print(f"❌ Real-time data test error: {e}")
            return False
    
    async def test_trade_placement(self, asset="EURUSD_otc", direction="call", amount=1.0, duration=60):
        """Test trade placement"""
        try:
            if not self.is_connected:
                return False
            
            print(f"\n🎯 Testing trade placement...")
            print(f"   Asset: {asset}")
            print(f"   Direction: {direction.upper()}")
            print(f"   Amount: ${amount}")
            print(f"   Duration: {duration}s")
            
            # Check balance first
            balance = await self.client.get_balance()
            if balance < amount:
                print(f"❌ Insufficient balance: ${balance:.2f} < ${amount}")
                return False
            
            print(f"✅ Sufficient balance: ${balance:.2f}")
            
            # Place trade
            status, buy_info = await self.client.buy(amount, asset, direction, duration)
            
            if status:
                print("✅ Trade placed successfully!")
                print(f"📊 Trade details: {buy_info}")
                
                # Wait a moment and check trade status
                await asyncio.sleep(5)
                
                # Get updated balance
                new_balance = await self.client.get_balance()
                print(f"💰 Updated balance: ${new_balance:.2f}")
                
                return True
            else:
                print(f"❌ Trade placement failed: {buy_info}")
                return False
                
        except Exception as e:
            print(f"❌ Trade placement error: {e}")
            return False
    
    async def test_multiple_assets(self):
        """Test data fetching for multiple assets"""
        try:
            if not self.is_connected:
                return False
            
            print(f"\n🌍 Testing multiple OTC assets...")
            
            test_assets = [
                "EURUSD_otc",
                "GBPUSD_otc", 
                "USDJPY_otc",
                "AUDUSD_otc"
            ]
            
            for asset in test_assets:
                print(f"\n📊 Testing {asset}...")
                
                # Get candles for this asset
                end_time = time.time()
                candles = await self.client.get_candles(asset, end_time, 300, 60)  # 5 candles
                
                if candles and len(candles) > 0:
                    latest = candles[-1]
                    print(f"✅ {asset}: Latest price {latest.get('close', 'N/A')}")
                else:
                    print(f"❌ {asset}: No data")
                
                await asyncio.sleep(1)  # Small delay between requests
            
            return True
            
        except Exception as e:
            print(f"❌ Multiple assets test error: {e}")
            return False
    
    async def simulate_bot_integration(self):
        """Simulate how a trading bot would integrate"""
        try:
            if not self.is_connected:
                return False
            
            print(f"\n🤖 Simulating bot integration...")
            
            # Simulate receiving signals from a bot
            bot_signals = [
                {
                    'asset': 'EURUSD_otc',
                    'direction': 'call',
                    'amount': 1.0,
                    'duration': 60,
                    'reason': 'RSI oversold + MACD bullish crossover'
                },
                {
                    'asset': 'GBPUSD_otc', 
                    'direction': 'put',
                    'amount': 1.0,
                    'duration': 120,
                    'reason': 'Resistance level reached + bearish divergence'
                }
            ]
            
            for i, signal in enumerate(bot_signals, 1):
                print(f"\n📡 Processing bot signal {i}/{len(bot_signals)}")
                print(f"   Asset: {signal['asset']}")
                print(f"   Direction: {signal['direction'].upper()}")
                print(f"   Amount: ${signal['amount']}")
                print(f"   Reason: {signal['reason']}")
                
                # Get current price for context
                try:
                    end_time = time.time()
                    candles = await self.client.get_candles(signal['asset'], end_time, 60, 60)
                    if candles:
                        current_price = candles[-1].get('close', 'N/A')
                        print(f"   Current Price: {current_price}")
                except:
                    pass
                
                # Check balance
                balance = await self.client.get_balance()
                
                if balance >= signal['amount']:
                    print(f"✅ Executing trade (Balance: ${balance:.2f})")
                    
                    # Execute trade
                    status, result = await self.client.buy(
                        signal['amount'],
                        signal['asset'], 
                        signal['direction'],
                        signal['duration']
                    )
                    
                    if status:
                        print(f"✅ Trade executed successfully!")
                        print(f"📊 Result: {result}")
                    else:
                        print(f"❌ Trade execution failed: {result}")
                else:
                    print(f"❌ Insufficient balance: ${balance:.2f} < ${signal['amount']}")
                
                # Wait between signals
                if i < len(bot_signals):
                    print("⏳ Waiting 10 seconds before next signal...")
                    await asyncio.sleep(10)
            
            return True
            
        except Exception as e:
            print(f"❌ Bot integration simulation error: {e}")
            return False
    
    async def close(self):
        """Close connection"""
        if self.client:
            await self.client.close()
            print("🔌 Connection closed")

async def main():
    """Main test function"""
    print("🚀 Starting PyQuotex Live Trading Test")
    print("="*60)
    
    # Initialize test
    test = LiveTradingTest(
        email="<EMAIL>",
        password="Uz2309##2309",
        demo_mode=True  # Use demo for testing
    )
    
    try:
        # Test 1: Connection
        print("📋 Test 1: Connection")
        if not await test.connect_with_retries():
            print("❌ Connection test failed - aborting")
            return
        
        # Test 2: Balance
        print("\n📋 Test 2: Balance Retrieval")
        await test.test_balance()
        
        # Test 3: Candle Data
        print("\n📋 Test 3: Historical Candle Data")
        await test.test_candle_data("EURUSD_otc", 60, 5)
        
        # Test 4: Multiple Assets
        print("\n📋 Test 4: Multiple Assets")
        await test.test_multiple_assets()
        
        # Test 5: Real-time Data
        print("\n📋 Test 5: Real-time Data Stream")
        await test.test_real_time_data("EURUSD_otc", 10)
        
        # Test 6: Trade Placement
        print("\n📋 Test 6: Trade Placement")
        await test.test_trade_placement("EURUSD_otc", "call", 1.0, 60)
        
        # Test 7: Bot Integration Simulation
        print("\n📋 Test 7: Bot Integration Simulation")
        await test.simulate_bot_integration()
        
        print("\n" + "="*60)
        print("✅ All tests completed!")
        print("🎯 PyQuotex is ready for bot integration!")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await test.close()

if __name__ == "__main__":
    asyncio.run(main())
