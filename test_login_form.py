import requests
from bs4 import BeautifulSoup

# Test the login form directly
session = requests.Session()
session.headers.update({
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
})

try:
    # First, get the login page to see the form structure
    print("Getting login page...")
    response = session.get("https://market-qx.pro/pt/sign-in/modal/")
    print(f"Status code: {response.status_code}")
    print(f"URL after redirect: {response.url}")
    
    if response.status_code == 200:
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Look for the token
        token_input = soup.find("input", {"name": "_token"})
        if token_input:
            print(f"Found token: {token_input.get('value')[:20]}...")
        else:
            print("No token found")
        
        # Look for the form
        form = soup.find("form")
        if form:
            print("Found form")
            inputs = form.find_all("input")
            for inp in inputs:
                print(f"Input: name='{inp.get('name')}', type='{inp.get('type')}'")
        else:
            print("No form found")
            
        # Check if we're being redirected or blocked
        if "cloudflare" in response.text.lower():
            print("Cloudflare protection detected")
        
        # Save the response for inspection
        with open("login_page.html", "w", encoding="utf-8") as f:
            f.write(response.text)
        print("Saved response to login_page.html")
        
    else:
        print(f"Failed to get login page: {response.status_code}")
        print(response.text[:500])

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
