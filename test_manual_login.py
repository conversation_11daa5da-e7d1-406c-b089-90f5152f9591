import requests
from bs4 import BeautifulSoup
import time
import random

def test_manual_login():
    session = requests.Session()
    
    # Set headers to mimic a real browser
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "en-US,en;q=0.9,pt;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Sec-Ch-Ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"Windows"',
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1"
    }
    
    session.headers.update(headers)
    
    try:
        # Step 1: Get the login page to extract the token
        print("Getting login page...")
        time.sleep(random.uniform(2, 4))
        
        response = session.get("https://market-qx.pro/pt/sign-in/")
        print(f"Login page status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"Failed to get login page: {response.text[:500]}")
            return
        
        # Parse the HTML to get the token
        soup = BeautifulSoup(response.content, 'html.parser')
        token_input = soup.find("input", {"name": "_token"})
        
        if not token_input:
            print("No token found in login page")
            return
        
        token = token_input.get("value")
        print(f"Token found: {token[:20]}...")
        
        # Step 2: Submit the login form
        print("Submitting login form...")
        time.sleep(random.uniform(1, 3))
        
        login_data = {
            "_token": token,
            "email": "<EMAIL>",
            "password": "Uz2309##2309",
            "remember": "1"
        }
        
        # Update headers for form submission
        session.headers.update({
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": "https://market-qx.pro/pt/sign-in/",
            "Origin": "https://market-qx.pro"
        })
        
        login_response = session.post("https://market-qx.pro/pt/sign-in/", data=login_data)
        print(f"Login response status: {login_response.status_code}")
        print(f"Login response URL: {login_response.url}")
        
        # Check if login was successful
        if "trade" in login_response.url:
            print("✅ Login successful!")
            
            # Try to get balance or account info
            print("Trying to access account info...")
            account_response = session.get("https://market-qx.pro/pt/trade/")
            print(f"Account page status: {account_response.status_code}")
            
            # Save cookies for potential reuse
            cookies = session.cookies.get_dict()
            print(f"Got {len(cookies)} cookies")
            
            return True
            
        else:
            print("❌ Login failed")
            
            # Parse error messages
            soup = BeautifulSoup(login_response.content, 'html.parser')
            
            # Look for error messages
            error_selectors = [
                ".hint--danger",
                ".input-control-cabinet__hint",
                ".alert-danger",
                ".error-message"
            ]
            
            for selector in error_selectors:
                error_element = soup.select_one(selector)
                if error_element:
                    print(f"Error message: {error_element.get_text().strip()}")
                    break
            else:
                # Save response for debugging
                with open("login_failed_response.html", "w", encoding="utf-8") as f:
                    f.write(login_response.text)
                print("No specific error found. Response saved to login_failed_response.html")
            
            return False
            
    except Exception as e:
        print(f"Error during login: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_manual_login()
    print(f"Login test result: {'SUCCESS' if success else 'FAILED'}")
