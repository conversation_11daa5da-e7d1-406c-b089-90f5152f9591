#!/usr/bin/env python3
"""
Test Menu - Simple test to verify the bot menu is working
"""

import sys
import os

# Add the train bot directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'train bot'))

try:
    from utils import print_colored
    
    print("🧪 TESTING MENU DISPLAY")
    print("=" * 60)
    
    # Test the menu display
    print_colored("🤖 PyQuotex Trading Bot", "TITLE", bold=True)
    print_colored("=" * 50, "SKY_BLUE")
    print_colored("1. 🎯 Interactive Mode", "MENU_OPTION")
    print_colored("2. 🎮 Demo Mode", "MENU_OPTION")
    print_colored("3. 🚀 Live Trading", "MENU_OPTION")
    print_colored("4. 💰 Check Balance", "MENU_OPTION")
    print_colored("5. 🚪 Exit", "MENU_OPTION")
    print_colored("=" * 50, "SKY_BLUE")
    
    print("\n✅ Menu display working correctly")
    
    # Test colored signal indicators
    print("\n🎯 TESTING SIGNAL INDICATORS")
    print("-" * 30)
    
    print_colored("📈 CALL Signal", "SUCCESS")
    print_colored("📉 PUT Signal", "SUCCESS") 
    print_colored("⚪ HOLD Signal", "SIGNAL_NOT_FOUND")
    
    print("\n✅ Signal indicators working correctly")
    
    # Test shutdown message
    print("\n🛑 TESTING SHUTDOWN MESSAGE")
    print("-" * 30)
    
    print_colored("=" * 80, "SKY_BLUE")
    print_colored("🛑 Bot stopped by the owner Muhammad Uzair", "WARNING", bold=True)
    print_colored("🎉 Hope your session was productive and successful!", "SUCCESS", bold=True)
    print_colored("💫 Thank you for using this Trading Model", "SKY_BLUE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    print("\n✅ Shutdown message working correctly")
    
    print("\n🎉 ALL VISUAL ELEMENTS WORKING!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
