import asyncio
from playwright.async_api import async_playwright

async def test_login_with_playwright():
    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(headless=False)  # Set to False to see what's happening
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        page = await context.new_page()
        
        try:
            print("Navigating to login page...")
            await page.goto("https://market-qx.pro/pt/sign-in/", timeout=30000)
            
            # Wait for page to load
            await page.wait_for_load_state("networkidle")
            
            print("Page loaded, checking for login form...")

            # Take a screenshot for debugging
            await page.screenshot(path="login_page_screenshot.png")
            print("Screenshot saved")

            # Save page content for debugging
            content = await page.content()
            with open("playwright_page.html", "w", encoding="utf-8") as f:
                f.write(content)
            print("Page content saved to playwright_page.html")

            # Check if we can find the login form - specifically in the active tab
            email_input = await page.query_selector('#tab-1 input[name="email"]')
            password_input = await page.query_selector('#tab-1 input[name="password"]')

            print(f"Email input found: {email_input is not None}")
            print(f"Password input found: {password_input is not None}")

            # Try to make sure the login tab is active
            login_tab = await page.query_selector('a[data-value="1"]')
            if login_tab:
                await login_tab.click()
                await page.wait_for_timeout(1000)  # Wait for tab switch
                print("Clicked login tab")

            # Try alternative selectors if needed
            if not email_input:
                email_input = await page.query_selector('#tab-1 input[type="email"]')
                print(f"Email input (by type) found: {email_input is not None}")

            if not password_input:
                password_input = await page.query_selector('#tab-1 input[type="password"]')
                print(f"Password input (by type) found: {password_input is not None}")
            
            if email_input and password_input:
                print("Login form found, filling credentials...")
                
                await email_input.fill("<EMAIL>")
                await password_input.fill("Uz2309##2309")
                
                # Look for submit button
                submit_button = await page.query_selector('button[type="submit"]') or await page.query_selector('input[type="submit"]')
                
                if submit_button:
                    print("Submitting login form...")
                    await submit_button.click()
                    
                    # Wait for response
                    await page.wait_for_load_state("networkidle")
                    
                    # Check if login was successful
                    current_url = page.url
                    print(f"After login URL: {current_url}")
                    
                    if "trade" in current_url:
                        print("Login successful!")
                        
                        # Try to get cookies/session data
                        cookies = await context.cookies()
                        print(f"Got {len(cookies)} cookies")
                        
                        # Look for session token or similar
                        for cookie in cookies:
                            if "session" in cookie['name'].lower() or "token" in cookie['name'].lower():
                                print(f"Found session cookie: {cookie['name']}")
                    else:
                        print("Login failed - not redirected to trade page")
                        
                        # Check for error messages
                        error_elements = await page.query_selector_all('.error, .alert-danger, .hint--danger')
                        for element in error_elements:
                            error_text = await element.text_content()
                            if error_text:
                                print(f"Error message: {error_text}")
                else:
                    print("Submit button not found")
            else:
                print("Login form not found")
                
                # Save page content for debugging
                content = await page.content()
                with open("playwright_page.html", "w", encoding="utf-8") as f:
                    f.write(content)
                print("Page content saved to playwright_page.html")
                
        except Exception as e:
            print(f"Error during login test: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_login_with_playwright())
