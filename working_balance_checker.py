#!/usr/bin/env python3
"""
Working Quotex Balance Checker
Uses HTTP API instead of WebSocket to avoid Cloudflare issues
"""

import asyncio
from pyquotex.http.login import Login
from pyquotex.http.settings import Settings

class QuotexBalanceChecker:
    def __init__(self, email, password, lang="pt"):
        self.email = email
        self.password = password
        self.lang = lang
        self.api = None
        self.login = None
        self.settings = None
        
    async def connect(self):
        """Connect and authenticate with Quotex"""
        try:
            print("🔐 Connecting to Quotex...")
            
            # Create a mock API object for login
            class MockAPI:
                def __init__(self, email, password, lang):
                    self.lang = lang
                    self.https_url = "https://market-qx.pro"
                    self.host = "market-qx.pro"
                    self.resource_path = "."
                    self.user_data_dir = "."
                    self.username = email
                    self.password = password
                    self.session_data = {
                        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                        "cookies": None,
                        "token": None
                    }
            
            self.api = MockAPI(self.email, self.password, self.lang)
            self.login = Login(self.api)
            
            print("🔑 Authenticating...")
            status, msg = await self.login(self.email, self.password)
            
            if not status:
                raise Exception(f"Login failed: {msg}")
            
            print("✅ Authentication successful!")
            
            # Get cookies from the login session
            if hasattr(self.login, 'response') and self.login.response:
                cookies = self.login.response.cookies
                cookie_string = "; ".join([f"{cookie.name}={cookie.value}" for cookie in cookies])
                self.api.session_data["cookies"] = cookie_string
            
            # Create settings instance for API calls
            self.settings = Settings(self.api)
            
            return True
            
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    async def get_balance(self):
        """Get account balance information"""
        try:
            if not self.settings:
                raise Exception("Not connected. Call connect() first.")
            
            print("💰 Fetching balance information...")
            
            # Get account settings which includes balance
            data = self.settings.get_settings()
            
            if not isinstance(data, dict) or 'data' not in data:
                raise Exception("Invalid response format")
            
            account_data = data['data']
            
            # Extract balance information
            balance_info = {
                'live_balance': float(account_data.get('liveBalance', 0)),
                'demo_balance': float(account_data.get('demoBalance', 0)),
                'is_demo': account_data.get('isDemo', False),
                'currency': account_data.get('currencyCode', 'USD'),
                'currency_symbol': account_data.get('currencySymbol', '$'),
                'user_id': account_data.get('nickname', 'Unknown'),
                'email': account_data.get('email', 'Unknown'),
                'country': account_data.get('countryName', 'Unknown'),
                'profile_level': account_data.get('profileLevel', 'Unknown'),
                'is_verified': account_data.get('is_verified_profile', False)
            }
            
            return balance_info
            
        except Exception as e:
            print(f"❌ Failed to get balance: {e}")
            return None
    
    def display_balance(self, balance_info):
        """Display balance information in a nice format"""
        if not balance_info:
            print("❌ No balance information available")
            return
        
        print("\n" + "="*50)
        print("📊 QUOTEX ACCOUNT BALANCE")
        print("="*50)
        print(f"👤 User: {balance_info['user_id']}")
        print(f"📧 Email: {balance_info['email']}")
        print(f"🌍 Country: {balance_info['country']}")
        print(f"⭐ Level: {balance_info['profile_level'].title()}")
        print(f"✅ Verified: {'Yes' if balance_info['is_verified'] else 'No'}")
        print("-"*50)
        
        current_mode = "Demo" if balance_info['is_demo'] else "Live"
        current_balance = balance_info['demo_balance'] if balance_info['is_demo'] else balance_info['live_balance']
        
        print(f"💼 Current Mode: {current_mode} Account")
        print(f"💰 Current Balance: {balance_info['currency_symbol']}{current_balance:.2f} {balance_info['currency']}")
        print("-"*50)
        print(f"🔴 Live Balance: {balance_info['currency_symbol']}{balance_info['live_balance']:.2f} {balance_info['currency']}")
        print(f"🔵 Demo Balance: {balance_info['currency_symbol']}{balance_info['demo_balance']:.2f} {balance_info['currency']}")
        print("="*50)

async def main():
    """Main function to check balance"""
    # Your Quotex credentials
    EMAIL = "<EMAIL>"
    PASSWORD = "Uz2309##2309"
    LANGUAGE = "pt"  # pt=Portuguese, en=English, es=Spanish
    
    # Create balance checker
    checker = QuotexBalanceChecker(EMAIL, PASSWORD, LANGUAGE)
    
    try:
        # Connect to Quotex
        if await checker.connect():
            # Get balance information
            balance = await checker.get_balance()
            
            # Display the results
            checker.display_balance(balance)
            
            print("\n✅ Balance check completed successfully!")
            return True
        else:
            print("❌ Failed to connect to Quotex")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Quotex Balance Checker...")
    success = asyncio.run(main())
    
    if success:
        print("🎉 Done!")
    else:
        print("💥 Failed!")
